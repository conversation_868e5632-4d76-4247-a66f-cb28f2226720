<script setup lang="ts">
import {getLocalizedConfigText} from '~/utils/i18n'
import teamConfigData from '~/config/teamConfig'
import type {SocialLink as ConfigSocialLink, TeamMemberConfig} from '~/types/site/team'

// 组件内部使用的 TeamMember 类型，name, role, description 已经是本地化后的字符串
interface LocalizedTeamMember {
    name: string
    role: string
    imageUrl: string
    description?: string
    // 直接使用配置中的 SocialLink 类型
    socials?: ConfigSocialLink[]
}

// Props 接口
interface Props {

    // title和团队成员列表的布局方式 vertical: 垂直布局 horizontal: 水平(左右)
    layout?: 'horizontal' | 'vertical'

    // 头像的形状
    avatarShape?: 'circle' | 'square'

    // 成员头像和名字、角色之间的布局方式
    memberInfoLayout?: 'horizontal' | 'vertical'
    itemsPerRowSm?: 1 | 2 | 3 | 4

    // 是否展示社交icon列表
    showSocialIcons?: boolean

    // 是否展示详细介绍
    showDetailedDescription?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    layout: 'horizontal',
    avatarShape: 'circle',
    memberInfoLayout: 'horizontal',
    itemsPerRowSm: 2,
    showSocialIcons: false,
    showDetailedDescription: false
})

// 计算本地化的默认标题
const localizedTitle = computed(() => {
    if (teamConfigData.title) {
        return getLocalizedConfigText(teamConfigData.title)
    }
    // Fallback
    return ''
})

// 计算本地化的默认描述
const localizedDescription = computed(() => {
    if (teamConfigData.description) {
        return getLocalizedConfigText(teamConfigData.description)
    }
    return ''
})

// 计算本地化后的团队数据
const localizedTeam = computed<LocalizedTeamMember[]>(() => {
    return teamConfigData.members.map((member: TeamMemberConfig) => ({
        imageUrl: member.imageUrl,
        // 本地化姓名
        name: getLocalizedConfigText(member.name),
        // 本地化角色
        role: getLocalizedConfigText(member.role),
        // 本地化描述 (可选)
        description: member.description ? getLocalizedConfigText(member.description) : undefined,
        // socials 直接使用，无需本地化
        socials: member.socials
    }))
})

// 动态计算成员信息容器的类
const memberInfoContainerClass = computed(() => {
    if (props.memberInfoLayout === 'vertical') {
        // 头像在上方，文字在下方，靠左对齐
        return 'flex flex-col items-start gap-y-3'
    }
    // 头像在左边，文字在右边，垂直居中对齐
    return 'flex items-center gap-x-6'
})

// 动态计算成员列表的 grid 列数类
const gridColsClass = computed(() => {
    // 基础为 grid-cols-1，适用于超小屏幕
    // itemsPerRowSm 应用于 sm 断点及以上
    switch (props.itemsPerRowSm) {
        case 1:
            return 'grid-cols-1 sm:grid-cols-1'
        case 3:
            return 'grid-cols-1 sm:grid-cols-3'
        case 4:
            return 'grid-cols-1 sm:grid-cols-4'
        case 2:
        default:
            return 'grid-cols-1 sm:grid-cols-2'
    }
})
</script>

<template>
  <div class="mx-auto max-w-7xl py-16">
    <slot name="before-layout"/>
    <!-- 根据 layout prop 动态应用不同的 grid 类 -->
    <div class="grid grid-cols-1 gap-x-8 gap-y-10" :class="{ 'md:grid-cols-3': layout === 'horizontal' }">
      <!-- 区域1: 标题和描述，提供 header 插槽 -->
      <div class="max-w-2xl" :class="{ 'md:col-span-1': layout === 'horizontal' }">
        <slot name="header"
              :localized-title="localizedTitle"
              :localized-description="localizedDescription"
              :localized-team="localizedTeam">
          <!-- 默认标题和描述 -->
          <h2 class="mb-6 text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            {{ localizedTitle }}
          </h2>
          <p class="text-base leading-7 text-gray-600 dark:text-gray-300">
            {{ localizedDescription }}
          </p>
        </slot>
      </div>

      <!-- 区域2: 团队成员列表 -->
      <slot name="before-members-layout"
            :localized-team="localizedTeam"/>
      <div :class="{ 'md:col-span-2': layout === 'horizontal' }">
        <ul role="list" class="grid gap-x-8 gap-y-12 sm:gap-y-16" :class="gridColsClass">
          <li v-for="(person, index) in localizedTeam" :key="index">
            <!-- 默认成员卡片渲染 -->
            <div>
              <slot name="member-info"
                    :member="person"
                    :component-props="props">
                <div :class="memberInfoContainerClass">
                  <img
                    :class="['h-16 w-16 object-cover', props.avatarShape === 'square' ? 'rounded-md' : 'rounded-full']"
                    :src="person.imageUrl"
                    :alt="`${person.name} avatar`">
                  <div>
                    <h3
                      class="text-lg font-semibold leading-7 tracking-tight text-gray-900 dark:text-white">
                      {{ person.name }}
                    </h3>
                    <p class="text-sm leading-6 text-gray-600 dark:text-gray-400">
                      {{ person.role }}
                    </p>
                  </div>
                </div>
              </slot>

              <slot name="member-details"
                    :member="person"
                    :component-props="props">
                <p v-if="props.showDetailedDescription && person.description"
                   class="mt-4 text-sm leading-6 text-gray-700 dark:text-gray-300">
                  {{ person.description }}
                </p>

                <div v-if="props.showSocialIcons && person.socials && person.socials.length > 0"
                     class="mt-4 flex items-center gap-x-3">
                  <a v-for="social in person.socials"
                     :key="social.name"
                     :href="social.url"
                     target="_blank"
                     rel="noopener noreferrer"
                     :aria-label="`${person.name} on ${social.name}`"
                     class="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400">
                    <span class="sr-only">{{ social.name }}</span>
                    <Icon :name="social.icon" class="h-5 w-5"/>
                  </a>
                </div>
              </slot>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- component-footer 插槽 -->
    <slot name="after-layout"/>
  </div>
</template>