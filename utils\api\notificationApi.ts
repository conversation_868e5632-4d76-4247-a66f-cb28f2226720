import type {ApiResponse} from '~/utils/http'
import {Post} from '~/utils/http'
import {apiLogger} from '~/utils/logger'
import type {NotificationSubscribeParams} from '~/types/api/params/NotificationSubscribeParams'

/**
 * 创建通知相关API
 */
function createNotificationApi() {
    // 基础URL
    const baseUrl = '/notification'

    /**
     * 订阅邮件通知
     * @param params 订阅参数
     * @returns 订阅结果
     */
    function subscribe(params: NotificationSubscribeParams): Promise<ApiResponse<null>> {
        apiLogger.info('订阅邮件通知', {target: params.target, type: params.type})
        return Post<null>(`${baseUrl}/subscribe`, params, {})
    }

    // 返回API对象
    return {
        subscribe
    }
}

// 导出通知API实例
export const notificationApi = createNotificationApi()
export default notificationApi