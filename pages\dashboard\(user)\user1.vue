<script setup lang="ts">
import {ref} from 'vue'
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from '@/components/ui/accordion'
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'
import {Label} from '@/components/ui/label'
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select'
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from '@/components/ui/table'
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationFirst,
    PaginationItem,
    PaginationLast,
    PaginationNext,
    PaginationPrevious
} from '@/components/ui/pagination'
// Assuming global Icon component
import {Icon} from '#components'
import type {PageParams} from '~/utils/http/types' // 导入 PageParams

// 定义用户查询参数接口
interface UserQueryParams extends PageParams { // 继承 PageParams
    username?: string;
    gender?: string;
    email?: string;
    status?: string;
    nickname?: string;
    phone?: string;
    // pageNum 和 pageSize 会从 PageParams 继承
    // 如果需要排序功能，可以取消注释下一行，并确保 PageParams 中也有此定义或在此处添加
    // sortSqlList?: string[];
}

// 搜索表单数据
const queryParams = ref<UserQueryParams>({
    username: '',
    gender: '',
    email: '',
    status: '',
    nickname: '',
    phone: '',
    // 初始页码
    pageNum: 1,
    // 初始每页条数
    pageSize: 10
})

// 表格模拟数据
const users = ref([
    {
        id: 1,
        username: 'QXRnNq3ny3xEL',
        gender: '女',
        nickname: 'Edward Thompson',
        phone: '15677334609',
        email: '<EMAIL>',
        status: '启用'
    },
    {
        id: 2,
        username: 'Xfmsh59jKzkLNA',
        gender: '女',
        nickname: 'Brenda Anderson',
        phone: '16671767247',
        email: '<EMAIL>',
        status: '禁用'
    },
    {
        id: 3,
        username: 'daSFU3uSQ6Vo',
        gender: '女',
        nickname: 'Sandra Davis',
        phone: '13102963056',
        email: '<EMAIL>',
        status: '启用'
    },
    {
        id: 4,
        username: 'VJk',
        gender: '女',
        nickname: 'Thomas Robinson',
        phone: '13785748503',
        email: '<EMAIL>',
        status: '启用'
    },
    {
        id: 5,
        username: 'fEXT2wx5p',
        gender: '女',
        nickname: 'Jennifer Jones',
        phone: '13707647854',
        email: '<EMAIL>',
        status: '禁用'
    },
    {
        id: 6,
        username: 'mb3tSlUx0KpG4j',
        gender: '女',
        nickname: 'Jessica Wilson',
        phone: '17813622546',
        email: '<EMAIL>',
        status: '禁用'
    },
    {
        id: 7,
        username: 'QvFkWZYLb2rHgRp',
        gender: '男',
        nickname: 'Barbara Brown',
        phone: '16604489417',
        email: '<EMAIL>',
        status: '启用'
    },
    {
        id: 8,
        username: 'nF4no',
        gender: '男',
        nickname: 'Amy Lewis',
        phone: '15924442406',
        email: '<EMAIL>',
        status: '禁用'
    },
    {
        id: 9,
        username: 'sN6z',
        gender: '女',
        nickname: 'Robert Jackson',
        phone: '13125819863',
        email: '<EMAIL>',
        status: '启用'
    },
    {
        id: 10,
        username: 'HWA6Mbn',
        gender: '女',
        nickname: 'Daniel Jackson',
        phone: '16652736362',
        email: '<EMAIL>',
        status: '禁用'
    }
])

// 已选中的用户ID数组
const selectedUserIds = ref<number[]>([])

// 表头定义
const tableColumns = [
    {key: 'selection', label: ''},
    {key: 'id', label: '序号'},
    {key: 'username', label: '用户名'},
    {key: 'gender', label: '性别'},
    {key: 'nickname', label: '昵称'},
    {key: 'phone', label: '手机号'},
    {key: 'email', label: '邮箱'},
    {key: 'status', label: '用户状态'},
    {key: 'actions', label: '操作'}
]

// 分页相关
// const currentPage = ref(1) // 由 queryParams.pageNum 替代
// const itemsPerPage = ref(10) // 由 queryParams.pageSize 替代
// 假设总共有200条数据
const totalItems = ref(200)

function handleSearch() {
    // 搜索逻辑，此处仅打印
    // 在实际搜索时，queryParams.value 将包含分页参数
    console.log('Searching with:', queryParams.value)
}

function handleReset() {
    queryParams.value = {
        username: '',
        gender: '',
        email: '',
        status: '',
        nickname: '',
        phone: '',
        // 重置时恢复默认页码
        pageNum: 1,
        // 重置时保持每页条数不变，或设置为默认值10
        pageSize: queryParams.value.pageSize || 10
    }
}

</script>

<template>
  <div class="p-4 space-y-4">
    <!-- 搜索区域 -->
    <Accordion type="single" collapsible class="w-full bg-card rounded-md shadow p-4">
      <AccordionItem value="item-1">
        <AccordionTrigger class="pt-0 pb-0 no-underline hover:no-underline">
          <div class="flex items-center space-x-2 cursor-pointer">
            <Icon name="lucide:search" class="h-5 w-5"/>
            <span>搜索</span>
          </div>
        </AccordionTrigger>
        <AccordionContent class="pb-0">
          <div class="mt-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div class="flex items-baseline gap-2">
                <Label for="username" class="text-sm shrink-0">用户名</Label>
                <Input id="username"
                       v-model="queryParams.username"
                       placeholder="请输入用户名"
                       class="flex-1"/>
              </div>
              <div class="flex items-baseline gap-2">
                <Label for="gender" class="text-sm shrink-0">性别</Label>
                <Select v-model="queryParams.gender" class="flex-1">
                  <SelectTrigger id="gender">
                    <SelectValue placeholder="请选择性别"/>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">
                      男
                    </SelectItem>
                    <SelectItem value="female">
                      女
                    </SelectItem>
                    <SelectItem value="other">
                      其他
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div class="flex items-baseline gap-2">
                <Label for="nickname" class="text-sm shrink-0">昵称</Label>
                <Input id="nickname"
                       v-model="queryParams.nickname"
                       placeholder="请输入昵称"
                       class="flex-1"/>
              </div>
              <div class="flex items-baseline gap-2">
                <Label for="phone" class="text-sm shrink-0">手机号</Label>
                <Input id="phone"
                       v-model="queryParams.phone"
                       placeholder="请输入手机号"
                       class="flex-1"/>
              </div>
              <div class="flex items-baseline gap-2">
                <Label for="email" class="text-sm shrink-0">邮箱</Label>
                <Input id="email"
                       v-model="queryParams.email"
                       placeholder="请输入邮箱"
                       class="flex-1"/>
              </div>
              <div class="flex items-baseline gap-2">
                <Label for="status" class="text-sm shrink-0">用户状态</Label>
                <Select v-model="queryParams.status" class="flex-1">
                  <SelectTrigger id="status">
                    <SelectValue placeholder="请选择用户状态"/>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">
                      启用
                    </SelectItem>
                    <SelectItem value="inactive">
                      禁用
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div class="flex justify-end space-x-2">
              <Button variant="outline" @click="handleReset">
                <Icon name="lucide:rotate-ccw" class="mr-2 h-4 w-4"/>
                重置
              </Button>
              <Button @click="handleSearch">
                <Icon name="lucide:search" class="mr-2 h-4 w-4"/>
                搜索
              </Button>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>

    <!-- 用户列表区域 -->
    <div class="bg-card p-4 rounded-md shadow">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">
          用户列表
        </h2>
        <div class="space-x-2">
          <Button variant="destructive" :disabled="selectedUserIds.length === 0">
            <Icon name="lucide:trash-2" class="mr-2 h-4 w-4"/>
            批量删除
          </Button>
          <Button variant="outline">
            <Icon name="lucide:plus-circle" class="mr-2 h-4 w-4"/>
            新增
          </Button>
          <Button variant="outline">
            <Icon name="lucide:refresh-cw" class="mr-2 h-4 w-4"/>
            刷新
          </Button>
        </div>
      </div>

      <!-- 表格 -->
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="w-[50px]">
              <!-- TODO 点击触发全选 -->
            </TableHead>
            <TableHead v-for="column in tableColumns.filter(c => c.key !== 'selection')" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="user in users" :key="user.id">
            <TableCell>
              <!-- TODO 触发多选 -->
            </TableCell>
            <TableCell>{{ user.id }}</TableCell>
            <TableCell>{{ user.username }}</TableCell>
            <TableCell>
              <span :class="{ 'text-pink-500': user.gender === '女', 'text-blue-500': user.gender === '男' }">
                {{ user.gender }}
              </span>
            </TableCell>
            <TableCell>{{ user.nickname }}</TableCell>
            <TableCell>{{ user.phone }}</TableCell>
            <TableCell>{{ user.email }}</TableCell>
            <TableCell>
              <span :class="[
                'px-2 py-1 rounded-full text-xs font-medium',
                user.status === '启用' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
              ]">
                {{ user.status }}
              </span>
            </TableCell>
            <TableCell class="space-x-2">
              <Button variant="ghost" size="sm">
                编辑
              </Button>
              <Button variant="ghost" size="sm" class="text-destructive hover:text-destructive">
                删除
              </Button>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <!-- 分页区域 -->
    <div class="flex items-center justify-between pt-4">
      <div class="text-sm text-muted-foreground">
        共 {{ totalItems }} 条记录
      </div>
      <Pagination v-slot="{ page }"
                  :total="totalItems"
                  :items-per-page="queryParams.pageSize || 10"
                  :sibling-count="1"
                  show-edges
                  :default-page="queryParams.pageNum || 1"
                  @update:page="(newPage) => queryParams.pageNum = newPage">
        <PaginationContent v-slot="{ items }" class="flex items-center gap-1">
          <PaginationFirst/>
          <PaginationPrevious/>

          <template v-for="(item, index) in items">
            <PaginationItem v-if="item.type === 'page'"
                            :key="index"
                            :value="item.value"
                            as-child>
              <Button class="w-10 h-10 p-0" :variant="item.value === page ? 'default' : 'outline'">
                {{ item.value }}
              </Button>
            </PaginationItem>
            <PaginationEllipsis v-else :key="item.type" :index="index"/>
          </template>

          <PaginationNext/>
          <PaginationLast/>
        </PaginationContent>
      </Pagination>
      <div class="flex items-center space-x-2 text-sm">
        <span>每页</span>
        <Select v-model:model-value="queryParams.pageSize"
                @update:model-value="(val) => queryParams.pageSize = Number(val)">
          <SelectTrigger class="w-[70px]">
            <SelectValue :placeholder="String(queryParams.pageSize || 10)"/>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">10</SelectItem>
            <SelectItem value="20">20</SelectItem>
            <SelectItem value="50">50</SelectItem>
            <SelectItem value="100">100</SelectItem>
          </SelectContent>
        </Select>
        <span>条</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 根据项目规范，HTML 缩进为 2 空格, TypeScript (setup) 缩进为 4 空格 */
/* Tailwind CSS 已通过类名应用样式，通常不需要额外的 scoped CSS */
</style>