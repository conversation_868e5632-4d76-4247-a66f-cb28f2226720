import withNuxt from './.nuxt/eslint.config.mjs'

// 基础配置
export default withNuxt({
    // 在这里可以添加自定义规则
    rules: {
        // 代码风格规则
        // 不使用分号
        'semi': [2, 'never'],
        'indent': [
            2,
            4,
            {
                // 对switch-case增加额外缩进规则
                SwitchCase: 1
            }
        ],
        // Vue 特定规则
        'vue/first-attribute-linebreak': 'off',
        'vue/html-indent': ['error', 2, {
            attribute: 1,
            baseIndent: 1,
            closeBracket: 0,
            alignAttributesVertically: true,
            ignores: []
        }],
        'vue/max-attributes-per-line': ['error', {
            singleline: {
                max: 3
            },
            multiline: {
                max: 1
            }
        }],
        // 不允许多个连续的空格
        'no-multi-spaces': 2,
        // 一元运算符后必须有空格
        'space-unary-ops': [2, {words: true, nonwords: false}],
        // 中缀操作符周围必须有空格
        'space-infix-ops': 2,
        // 代码块前必须有空格
        'space-before-blocks': [2, 'always'],
        // 不允许混合使用空格和制表符
        // 'no-mixed-spaces-and-tabs': 2,
        // 连续空行不超过 1 行
        'no-multiple-empty-lines': [2, {max: 1}],
        // 行尾不允许有空格
        'no-trailing-spaces': [2, {skipBlankLines: true}],
        // 属性名和点运算符之间不能有空格
        'no-whitespace-before-property': 2,
        // 不允许出现不规则的空白字符
        'no-irregular-whitespace': 'off',
        // 圆括号内不能有空格
        'space-in-parens': [2, 'never'],
        // 逗号不允许有拖尾
        'comma-dangle': [2, 'never'],
        // 数组括号内不允许有空格
        'array-bracket-spacing': [2, 'never'],
        // 对象括号内不允许有空格
        'object-curly-spacing': [2, 'never'],
        // 行宽最大为 300 字符
        'max-len': ['error', {code: 300}],
        // 运算符换行时，运算符在行首
        'operator-linebreak': [2, 'before'],
        // 逗号风格：换行时在行尾
        'comma-style': [2, 'last'],
        // 不允许出现多余的分号
        'no-extra-semi': 2,
        // 使用大括号包裹所有控制结构，包括单行语句
        'curly': [2, 'all'],
        // 属性名与冒号之间不能有空格，冒号后必须有空格
        'key-spacing': [2, {beforeColon: false, afterColon: true}],
        // 逗号后必须有空格
        'comma-spacing': [2, {before: false, after: true}],
        // 分号后必须有空格
        'semi-spacing': [2, {before: false, after: true}],
        // 强制使用驼峰命名法
        'camelcase': [1, {properties: 'always'}],
        // 构造函数首字母必须大写
        'new-cap': ['error', {newIsCap: true, capIsNew: false}],
        // 注释后必须有空格
        'spaced-comment': [2, 'always'],
        // 不允许行内注释
        'no-inline-comments': 2,
        // 强制使用全等 (===) 运算符
        'eqeqeq': [2, 'always', {null: 'ignore'}],
        // 禁止 else 语句，如果 if 语句中已返回值
        'no-else-return': [1, {allowElseIf: false}],
        // 禁止在循环中定义函数
        'no-loop-func': 2,
        // 允许在case块中使用词法声明
        'no-case-declarations': 0,
        'no-restricted-syntax': [
            1,
            {
                selector: 'BinaryExpression[operator=\'instanceof\']',
                // 不建议使用 instanceof 来检测对象类型
                message: 'Use \'instanceof\' for object type detection.'
            },
            {
                selector: 'BinaryExpression[operator=\'typeof\']',
                // 不建议使用 typeof 来检测类型
                message: 'Use \'typeof\' for type detection.'
            },
            {
                selector: 'CallExpression[callee.name=\'parseInt\']',
                // 不建议使用 parseInt 来移除小数点
                message: 'Use Math.floor, Math.round, or Math.ceil instead of parseInt to remove decimal points.'
            }
        ],
        // 禁止隐式类型转换
        'no-implicit-coercion': [1, {allow: ['!!']}],
        // parseInt 函数必须指定进制
        'radix': [2, 'always'],
        // 强制使用单引号
        'quotes': [2, 'single'],
        // 不允许使用 Array 构造函数
        'no-array-constructor': 2,
        'max-lines-per-function': [
            1,
            {
                // 函数最大行数为 100 行
                max: 100,
                // 跳过注释行
                skipComments: true,
                // 跳过空行
                skipBlankLines: true,
                // 对立即调用的函数表达式 (IIFE) 应用规则
                IIFEs: true
            }
        ],
        // 函数参数最大数量为 6
        'max-params': [1, 6],
        // 禁止使用 eval
        'no-eval': 2,
        // 建议使用 const 声明不变的变量
        'prefer-const': 1,
        // 建议使用 let/const 替代 var
        'no-var': 1,
        'prefer-destructuring': [
            1,
            // 建议使用解构赋值
            {object: true, array: false}
        ],
        // 建议使用模板字符串
        'prefer-template': 1,
        // 模板字符串中的花括号内不允许有空格
        'template-curly-spacing': [2, 'never'],
        // 禁止重复导入
        'no-duplicate-imports': 2,
        // TypeScript 特定规则
        // 禁止未使用的变量
        '@typescript-eslint/no-unused-vars': 'error',
        // 允许省略函数的返回类型
        '@typescript-eslint/explicit-module-boundary-types': 'off'
    }
})