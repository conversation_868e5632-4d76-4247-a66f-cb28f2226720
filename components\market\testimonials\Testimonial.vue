<script setup lang="ts">
import TestimonialCard from './TestimonialCard.vue'
import {testimonialApi} from '@/utils/api/testimonialApi'
import type {TestimonialResponse} from '@/types/api/response/TestimonialResponse'
import type {PageParams} from '~/utils/http'
import {logger} from '@/utils/logger'
import AutoScrollContainer from '@/components/common/AutoScrollContainer.vue'
import {cn} from '@/lib/utils'
import {getLocalizedConfigText} from '@/utils/i18n'
import {testimonialConfig} from '@/config/testimonial'

// --- Props 定义 ---
interface DisplayConfig {
    rows: number;
    displayStyle: 'default' | 'overlay';
}

const props = withDefaults(defineProps<{
    // 使用 Partial 允许部分覆盖默认值
    pc?: Partial<DisplayConfig>;
    mobile?: Partial<DisplayConfig>;
}>(), {
    // 默认 PC 配置：网格布局
    pc: () => ({
        rows: 1,
        displayStyle: 'default'
    }),
    // 默认 Mobile 配置：滚动布局, 1行
    mobile: () => ({
        rows: 1,
        displayStyle: 'overlay'
    })
})

// --- 数据获取 ---
const testimonialsData = ref<TestimonialResponse[]>([])
const isLoading = ref(true)

onMounted(async () => {
    isLoading.value = true
    try {
        const params: PageParams & Partial<TestimonialResponse> = {
            pageNum: 1,
            // 获取更多数据以适应滚动 (例如 12 条)
            pageSize: 12,
            sort: 'createTime,desc'
        }
        const response = await testimonialApi.page(params)

        if (response && response.code === 200 && response.data && response.data.result) {
            testimonialsData.value = response.data.result
        } else {
            logger.error('获取评论失败或数据格式错误:', response)
            testimonialsData.value = []
        }
    } catch (err) {
        logger.error('请求评论异常:', err)
        testimonialsData.value = []
    } finally {
        isLoading.value = false
    }
})

// --- 设备检测 (基础版) ---
const isMobile = ref(false)
let resizeObserver: ResizeObserver | null = null

const checkDevice = () => {
    // 使用 768px (Tailwind md breakpoint) 作为分界点
    if (typeof window !== 'undefined') {
        isMobile.value = window.innerWidth < 768
    }
}

onMounted(() => {
    // 初始检查
    checkDevice()
    // 使用 ResizeObserver 监听窗口大小变化可能比 resize 事件更高效
    if (typeof window !== 'undefined') {
        resizeObserver = new ResizeObserver(checkDevice)
        resizeObserver.observe(document.body)
    }
})

onBeforeUnmount(() => {
    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }
})

// --- 计算当前配置 ---
// 合并默认值和传入的 props
const pcConfig = computed<DisplayConfig>(() => ({
    // 默认值
    rows: 1,
    displayStyle: 'default',
    // 覆盖默认值
    ...props.pc
}))
const mobileConfig = computed<DisplayConfig>(() => ({
    // 默认值
    rows: 1,
    displayStyle: 'overlay',
    // 覆盖默认值
    ...props.mobile
}))

const activeDisplayStyle = computed(() => {
    return isMobile.value ? mobileConfig.value.displayStyle : pcConfig.value.displayStyle
})

const activeRows = computed(() => {
    return isMobile.value ? mobileConfig.value.rows : pcConfig.value.rows
})

</script>

<template>
  <!-- 骨架屏 -->
  <section v-if="isLoading" class="relative isolate bg-background py-16">
    <div class="mx-auto">
      <!-- 标题骨架 -->
      <div class="mx-auto max-w-xl text-center mb-12 md:mb-16">
        <Skeleton class="h-10 w-3/4 mx-auto"/>
        <Skeleton class="h-6 w-1/2 mx-auto mt-4"/>
      </div>

      <!-- 默认网格骨架 -->
      <div v-if="activeDisplayStyle === 'default'" class="mx-auto flow-root max-w-2xl lg:mx-0 lg:max-w-none">
        <div class="sm:columns-2 lg:columns-3">
          <div v-for="i in 6" :key="i" class="mb-8 sm:mb-6 break-inside-avoid">
            <div class="rounded-lg bg-card text-card-foreground shadow-sm p-6 pt-8 h-52">
              <Skeleton class="h-4 w-full mb-2"/>
              <Skeleton class="h-4 w-5/6 mb-4"/>
              <div class="flex items-center gap-x-4 mt-6">
                <Skeleton class="h-10 w-10 rounded-full"/>
                <div class="flex-1 space-y-2">
                  <Skeleton class="h-4 w-1/3"/>
                  <Skeleton class="h-4 w-1/4"/>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Overlay 滚动骨架 -->
      <div v-else-if="activeDisplayStyle === 'overlay'" class="overflow-hidden"> <!-- 防止骨架内容溢出影响布局 -->
        <div v-for="row in activeRows" :key="row" class="flex justify-center mb-4 last:mb-0">
          <div v-for="i in 3" :key="i" class="mx-4 flex-shrink-0 w-[350px]">
            <div class="rounded-lg bg-card text-card-foreground shadow-sm p-6 pt-8 h-72">
              <Skeleton class="h-4 w-full mb-2"/>
              <Skeleton class="h-4 w-5/6 mb-4"/>
              <div class="flex items-center gap-x-4 mt-6">
                <Skeleton class="h-10 w-10 rounded-full"/>
                <div class="flex-1 space-y-2">
                  <Skeleton class="h-4 w-1/3"/>
                  <Skeleton class="h-4 w-1/4"/>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div v-else-if="!isLoading && testimonialsData.length > 0">
    <slot name="before-layout"/>

    <!-- 实际内容区域 -->
    <section class="relative isolate bg-background py-16">
      <!-- Header 插槽，包含默认标题和描述 -->
      <slot name="header"
            :localized-title="getLocalizedConfigText(testimonialConfig.title)"
            :localized-description="getLocalizedConfigText(testimonialConfig.description)">
        <div class="mx-auto max-w-xl text-center mb-10">
          <h2 class="text-3xl font-semibold leading-tight tracking-tight text-foreground sm:text-4xl mb-2">
            {{ getLocalizedConfigText(testimonialConfig.title) }}
          </h2>
          <p class="text-lg leading-8 text-muted-foreground">
            {{ getLocalizedConfigText(testimonialConfig.description) }}
          </p>
        </div>
      </slot>

      <slot name="before-show-testimonial-content"/>

      <!-- 默认网格布局 -->
      <div v-if="activeDisplayStyle === 'default'" class="mx-auto flow-root max-w-2xl lg:mx-0 lg:max-w-none">
        <div class="sm:columns-2 lg:columns-3">
          <slot v-for="(testimonial, index) in testimonialsData"
                :key="index"
                :testimonial="testimonial"
                name="default-layout-content">
            <TestimonialCard
              :testimonial="testimonial"
              class="mb-6 sm:mb-6"/>
          </slot>
        </div>
      </div>

      <!-- Overlay 滚动布局 -->
      <AutoScrollContainer v-else
                           :rows="activeRows"
                           pause-on-hover
                           :fade-edges="true">
        <slot v-for="(testimonial, index) in testimonialsData"
              :key="index"
              :testimonial="testimonial"
              name="overlay-layout-content">
          <TestimonialCard
            :testimonial="testimonial"
            :class="cn(
              'mx-2.5 flex-shrink-0 w-[350px]',
              activeDisplayStyle === 'overlay' && 'h-72 overflow-hidden'
            )"/>
        </slot>
      </AutoScrollContainer>
    </section>
    <slot name="after-layout"/>
  </div>
</template>