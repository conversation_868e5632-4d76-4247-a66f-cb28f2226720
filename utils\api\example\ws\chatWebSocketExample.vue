<template>
  <div>
    <h2>WebSocket API 使用示例</h2>
    <button @click="requestHistory">
      查询历史消息
    </button>
    <div class="message-container">
      <h3>收到的历史消息:</h3>
      <ul>
        <li v-for="msg in messages" :key="msg.id">
          <strong>{{ msg.sender }}:</strong> {{ msg.content }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import type {WebSocketResponse} from '~/utils/http/types'
import {ChatWsService, type HistoryMsg} from '~/utils/api/example/ws/chatExampleWs.service'

// 使用 ref 来持有服务实例和消息数据
const chatApi = ref<ChatWsService>()
const messages = ref<HistoryMsg[]>([])

// 在组件挂载时初始化WebSocket
onMounted(() => {
    initializeChat()
})

// 在组件卸载前清理资源
onBeforeUnmount(() => {
    if (chatApi.value) {
        chatApi.value.close()
    }
})

/**
 * 初始化WebSocket服务实例和连接
 */
const initializeChat = () => {
    // 1. 创建一个全新的、独立的service实例
    chatApi.value = new ChatWsService()

    // 2. 初始化这个实例的连接
    chatApi.value.init({
        nodeName: 'vue-example-component-123'
    })

    // 3. 为这个实例订阅事件
    chatApi.value.on<HistoryMsg[]>('QUERY_HISTORY_MSG_RESPONSE', handleHistoryMessages)
}

// ============= 处理业务逻辑 ===============
/**
 * 触发查询历史消息的请求
 */
const requestHistory = () => {
    if (chatApi.value) {
        chatApi.value.queryHistoryMsgList({
            contactId: 'user-contact-456',
            limit: 10
        })
    } else {
        logger.error('Chat API服务尚未初始化。')
    }
}

/**
 * 处理收到的历史消息响应
 * @param response
 */
const handleHistoryMessages = (response: WebSocketResponse<HistoryMsg[]>) => {
    if (response.code === 200 && response.data) {
        logger.info('成功获取历史消息:', response.data)
        messages.value = response.data
    } else {
        logger.error('获取历史消息失败:', response.message)
    }
}
</script>
