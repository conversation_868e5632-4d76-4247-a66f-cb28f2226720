<script setup lang="ts">
import {bentoGridConfig} from '~/config/bentoGrid'
import type {LocalizedBentoItem} from '~/types/site/bentoGrid'
import {getLocalizedConfigText} from '~/utils/i18n'
import AutoScrollContainer from '~/components/common/AutoScrollContainer.vue'

const props = defineProps({
    mobileLayout: {
        type: String as PropType<'default' | 'overlay'>,
        default: 'default',
        validator: (value: string) => ['default', 'overlay'].includes(value)
    }
})

const isScreenMdOrLarger = ref(false)

const checkScreenSize = () => {
    if (typeof window !== 'undefined') {
        // Tailwind's md breakpoint
        isScreenMdOrLarger.value = window.innerWidth >= 768
    }
}

onMounted(() => {
    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
    window.removeEventListener('resize', checkScreenSize)
})

const calculateItemStyle = (
    item: LocalizedBentoItem,
    bentoRow: LocalizedBentoItem[]
) => {
    if (!isScreenMdOrLarger.value || bentoRow.length <= 0) {
        // 对于移动设备或行内无项目，不应用特殊flex-basis，依赖w-full等class
        return {}
    }

    const totalProportion = bentoRow.reduce((sum, current) => sum + current.proportion, 0)

    if (totalProportion === 0) {
        // 安全回退：如果总比例为0（不应发生），则平均分配
        return {flexBasis: `${100 / bentoRow.length}%`}
    }

    // gap-6 对应 1.5rem
    const gapSizeRem = 1.5
    const numItems = bentoRow.length
    // 只有当项目数大于1时，才存在间隙
    const totalGapRemForRow = numItems > 1 ? (numItems - 1) * gapSizeRem : 0

    const itemProportionRatio = item.proportion / totalProportion

    if (numItems <= 1) {
        // 如果只有一个项目，它占据全部宽度，不需要计算间隙
        return {flexBasis: '100%'}
    }

    // 每个项目的flex-basis是其占比宽度减去其应承担的间隙部分
    const basisPercentage = itemProportionRatio * 100
    const reductionForGapsRem = itemProportionRatio * totalGapRemForRow

    return {
        flexBasis: `calc(${basisPercentage}% - ${reductionForGapsRem}rem)`
    }
}

const originalItems = bentoGridConfig.items

const localizedBentoConfig = computed<LocalizedBentoItem[][]>(() => {
    return originalItems.map(row =>
        row.map(item => {
            const localizedItem: Omit<LocalizedBentoItem, 'type'> & { type?: string } = {
                proportion: item.proportion,
                title: getLocalizedConfigText(item.title),
                description: getLocalizedConfigText(item.description),
                imageUrl: item.imageUrl,
                icon: item.icon,
                backgroundColor: item.backgroundColor
            }

            if (item.type) {
                localizedItem.type = getLocalizedConfigText(item.type)
            }

            return localizedItem as LocalizedBentoItem
        })
    )
})

const flatLocalizedBentoItems = computed<LocalizedBentoItem[]>(() => {
    return localizedBentoConfig.value.flat()
})
</script>

<template>
  <div class="py-16">
    <slot name="before-bento-grid"/>
    <div v-if="props.mobileLayout === 'overlay'">
      <!-- Overlay Layout for Mobile: Visible by default, hidden on lg screens and up -->
      <div class="lg:hidden">
        <AutoScrollContainer :rows="localizedBentoConfig.length || 1"
                             :pause-on-hover="true"
                             :fade-edges="false"
                             class="py-4">
          <div v-for="(item, globalIndex) in flatLocalizedBentoItems"
               :key="`scroll-item-${globalIndex}`"
               class="m-2 inline-block align-top">
            <div :class="[
              'p-4 rounded-lg shadow-md w-72 h-auto flex flex-col',
              item.backgroundColor ? item.backgroundColor.light : 'bg-card',
              item.backgroundColor && item.backgroundColor.dark ? `dark:${item.backgroundColor.dark}` : '',
              !item.backgroundColor && 'text-card-foreground'
            ]">
              <slot name="image" :item="item" v-bind="item">
                <img v-if="item.imageUrl"
                     :src="item.imageUrl"
                     :alt="item.title"
                     class="mb-3 rounded-md w-full h-40 object-cover">
              </slot>
              <div class="flex-grow">
                <slot name="type" :item="item" v-bind="item">
                  <div v-if="item.type" class="text-xs text-muted-foreground font-medium mb-1 tracking-wider uppercase">
                    {{ item.type }}
                  </div>
                </slot>
                <slot name="title" :item="item" v-bind="item">
                  <h3 v-if="item.title" class="text-md font-semibold mb-1 text-foreground truncate flex items-center">
                    <Icon v-if="item.icon"
                          :name="item.icon"
                          class="text-muted-foreground mr-1.5 h-4 w-4 inline-block shrink-0"/>
                    <span class="truncate">{{ item.title }}</span>
                  </h3>
                </slot>
                <slot name="description" :item="item" v-bind="item">
                  <p class="text-muted-foreground text-xs leading-normal line-clamp-2">
                    {{ item.description }}
                  </p>
                </slot>
              </div>
            </div>
          </div>
        </AutoScrollContainer>
      </div>

      <!-- Default Grid Layout for Desktop (part of overlay mobileLayout): Hidden by default, visible on lg screens and up -->
      <div class="hidden lg:grid grid-cols-1 gap-6">
        <div v-for="(bentoRow, rowIndex) in localizedBentoConfig"
             :key="rowIndex"
             class="flex flex-col gap-6 md:flex-row">
          <div v-for="(item, itemIndex) in bentoRow"
               :key="itemIndex"
               :class="[
                 'p-6 rounded-xl shadow-lg flex flex-col transition-all hover:shadow-xl h-auto',
                 'w-full',
                 item.backgroundColor ? item.backgroundColor.light : 'bg-card',
                 item.backgroundColor && item.backgroundColor.dark ? `dark:${item.backgroundColor.dark}` : '',
                 !item.backgroundColor && 'text-card-foreground',
                 'md:grow-0 md:shrink-0'
               ]"
               :style="calculateItemStyle(item, bentoRow)">
            <slot name="image" :item="item" v-bind="item">
              <img v-if="item.imageUrl"
                   :src="item.imageUrl"
                   :alt="item.title"
                   class="mb-4 md:mb-6 rounded-lg w-full h-64 object-cover">
            </slot>

            <div class="flex-grow flex flex-col">
              <slot name="type" :item="item" v-bind="item">
                <div v-if="item.type" class="text-sm text-muted-foreground font-medium mb-2 tracking-wider uppercase">
                  {{ item.type }}
                </div>
              </slot>

              <slot name="title" :item="item" v-bind="item">
                <h3 v-if="item.title" class="text-xl lg:text-2xl font-semibold mb-2 text-foreground flex items-center">
                  <Icon v-if="item.icon" :name="item.icon" class="text-muted-foreground mr-2 h-5 w-5 shrink-0"/>
                  <span>{{ item.title }}</span>
                </h3>
              </slot>

              <slot name="description" :item="item" v-bind="item">
                <p class="text-muted-foreground text-sm leading-relaxed flex-grow">
                  {{ item.description }}
                </p>
              </slot>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Default Layout: Rendered if mobileLayout is 'default' -->
    <div v-else class="grid grid-cols-1 gap-6">
      <div v-for="(bentoRow, rowIndex) in localizedBentoConfig" :key="rowIndex" class="flex flex-col gap-6 md:flex-row">
        <div v-for="(item, itemIndex) in bentoRow"
             :key="itemIndex"
             :class="[
               'p-6 rounded-xl shadow-lg flex flex-col transition-all hover:shadow-xl h-auto',
               'w-full',
               item.backgroundColor ? item.backgroundColor.light : 'bg-card',
               item.backgroundColor && item.backgroundColor.dark ? `dark:${item.backgroundColor.dark}` : '',
               !item.backgroundColor && 'text-card-foreground',
               'md:grow-0 md:shrink-0'
             ]"
             :style="calculateItemStyle(item, bentoRow)">
          <slot name="image" :item="item" v-bind="item">
            <img v-if="item.imageUrl"
                 :src="item.imageUrl"
                 :alt="item.title"
                 class="mb-4 md:mb-6 rounded-lg w-full h-64 object-cover">
          </slot>

          <div class="flex-grow flex flex-col">
            <slot name="type" :item="item" v-bind="item">
              <div v-if="item.type" class="text-sm text-muted-foreground font-medium mb-2 tracking-wider uppercase">
                {{ item.type }}
              </div>
            </slot>

            <slot name="title" :item="item" v-bind="item">
              <h3 v-if="item.title" class="text-xl lg:text-2xl font-semibold mb-2 text-foreground flex items-center">
                <Icon v-if="item.icon" :name="item.icon" class="text-muted-foreground mr-2 h-5 w-5 shrink-0"/>
                <span>{{ item.title }}</span>
              </h3>
            </slot>

            <slot name="description" :item="item" v-bind="item">
              <p class="text-muted-foreground text-sm leading-relaxed flex-grow">
                {{ item.description }}
              </p>
            </slot>
          </div>
        </div>
      </div>
    </div>

    <slot name="after-bento-grid"/>
  </div>
</template>