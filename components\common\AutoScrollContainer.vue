<script setup lang="ts">
import {
    Comment,
    type ComponentPublicInstance,
    computed,
    Fragment,
    nextTick,
    onBeforeUnmount,
    useSlots,
    type VNode,
    watch
} from 'vue'
import {cn} from '@/lib/utils'
import siteConfig from '@/config/site'
import {logger} from '@/utils/logger'

const props = withDefaults(defineProps<{
    pauseOnHover?: boolean
    rows?: number
    fadeEdges?: boolean
}>(), {
    pauseOnHover: true,
    rows: 1,
    fadeEdges: false
})

const slots = useSlots()
const wrapperRef = ref<HTMLElement | null>(null)
const startAnimation = ref(false)
let intersectionObserver: IntersectionObserver | null = null
let resizeObserverInternal: ResizeObserver | null = null
let resizeObserverBody: ResizeObserver | null = null

const isMobileContext = ref(false)

const checkDeviceSize = () => {
    if (typeof window !== 'undefined') {
        isMobileContext.value = window.innerWidth < 768
    }
}

const contentRefs = ref<Array<Element | ComponentPublicInstance | null>>([])
const calculatedDurations = ref<Array<string>>([])

const currentSpeed = computed(() => {
    const speedConfig = siteConfig.autoScrollSpeed
    if (isMobileContext.value) {
        return typeof speedConfig?.mobile === 'number' && speedConfig.mobile > 0 ? speedConfig.mobile : 50
    }
    return typeof speedConfig?.pc === 'number' && speedConfig.pc > 0 ? speedConfig.pc : 100
})

const calculateDurationForRow = (rowIndex: number) => {
    const contentElement = contentRefs.value[rowIndex]
    let scrollWidth = 0
    if (contentElement instanceof Element) {
        scrollWidth = contentElement.scrollWidth
    } else if (contentElement?.$el instanceof Element) {
        scrollWidth = contentElement.$el.scrollWidth
    } else {
        logger.warn(`Could not get scrollWidth for row ${rowIndex}. Ref is:`, {ref: contentElement}, {moduleId: 'AutoScrollContainer'})
    }

    if (scrollWidth > 0 && currentSpeed.value > 0) {
        const durationInSeconds = scrollWidth / currentSpeed.value
        calculatedDurations.value[rowIndex] = `${durationInSeconds.toFixed(2)}s`
    } else {
        calculatedDurations.value[rowIndex] = '40s'
    }
}

const recalculateAllDurations = () => {
    nextTick(() => {
        rowsData.value.forEach((_, index) => {
            calculateDurationForRow(index)
        })
    })
}

function getValidSlotChildren(nodes: Readonly<VNode[]>): VNode[] {
    const validNodes: VNode[] = []
    if (!nodes) {
        return validNodes
    }

    for (const vnode of nodes) {
        if (vnode.type === Fragment) {
            const {children} = vnode
            if (Array.isArray(children)) {
                validNodes.push(...getValidSlotChildren(children as VNode[]))
            } else if (children && typeof children === 'object' && 'default' in children && typeof children.default === 'function') {
                const defaultNodes = children.default()
                if (Array.isArray(defaultNodes)) {
                    validNodes.push(...getValidSlotChildren(defaultNodes))
                }
            }
        } else if (
            vnode.type !== Comment
            && (typeof vnode.type === 'object' || typeof vnode.type === 'string')
            && !(typeof vnode.children === 'string' && !vnode.children.trim())
        ) {
            validNodes.push(vnode)
        }
    }
    return validNodes
}

const slotChildren = computed(() => {
    const defaultSlotNodes = slots.default ? slots.default() : []
    return getValidSlotChildren(defaultSlotNodes)
})

const rowsData = computed(() => {
    const numRows = Math.max(1, props.rows)
    const children = slotChildren.value
    const result: VNode[][] = Array.from({length: numRows}, () => [])

    if (!children || children.length === 0) {
        return result
    }

    children.forEach((child, index) => {
        result[index % numRows].push(child)
    })

    return result
})

const initializeRefs = (numRows: number) => {
    calculatedDurations.value = Array(numRows).fill('40s')
    contentRefs.value = Array(numRows).fill(null)
}

const hasContent = computed(() => rowsData.value.some(row => row.length > 0))

onMounted(() => {
    initializeRefs(rowsData.value.length)
    checkDeviceSize()

    if (wrapperRef.value && hasContent.value) {
        const options = {root: null, rootMargin: '0px', threshold: 0.1}
        intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    startAnimation.value = true
                    if (intersectionObserver && wrapperRef.value) {
                        intersectionObserver.unobserve(wrapperRef.value)
                        intersectionObserver = null
                    }
                    nextTick(() => {
                        recalculateAllDurations()
                        setupResizeObserverInternal()
                    })
                }
            })
        }, options)
        intersectionObserver.observe(wrapperRef.value)
    } else {
        nextTick(() => setupResizeObserverInternal())
    }

    if (typeof window !== 'undefined') {
        resizeObserverBody = new ResizeObserver(checkDeviceSize)
        resizeObserverBody.observe(document.body)
    }
})

const setupResizeObserverInternal = () => {
    if (resizeObserverInternal) {
        resizeObserverInternal.disconnect()
    }

    if (typeof window !== 'undefined' && hasContent.value) {
        resizeObserverInternal = new ResizeObserver(() => recalculateAllDurations())
        contentRefs.value.forEach(refValue => {
            let elementToObserve: Element | null = null
            if (refValue instanceof Element) {
                elementToObserve = refValue
            } else if (refValue?.$el instanceof Element) {
                elementToObserve = refValue.$el
            }
            if (elementToObserve) {
                resizeObserverInternal!.observe(elementToObserve)
            }
        })
    }
}

onBeforeUnmount(() => {
    if (intersectionObserver) {
        intersectionObserver.disconnect()
    }
    if (resizeObserverInternal) {
        resizeObserverInternal.disconnect()
    }
    if (resizeObserverBody) {
        resizeObserverBody.disconnect()
    }
})

watch(slotChildren, (newChildren, oldChildren) => {
    if (newChildren.length !== oldChildren?.length || props.rows !== rowsData.value.length) {
        initializeRefs(rowsData.value.length)
    }
    recalculateAllDurations()
    nextTick(() => setupResizeObserverInternal())
}, {deep: true})

watch(() => props.rows, (newRows) => {
    initializeRefs(newRows)
    nextTick(() => {
        recalculateAllDurations()
        setupResizeObserverInternal()
    })
})

function getVNodeKey(vnode: VNode, index: number): string | number | symbol {
    return vnode.key ?? index
}

function getVNodeCopyKey(vnode: VNode, index: number): string {
    const originalKey = getVNodeKey(vnode, index)
    return `${String(originalKey)}_copy`
}
</script>

<template>
  <div ref="wrapperRef"
       class="scroll-outer-wrapper w-full overflow-hidden"
       :class="{ 'fade-edges-active': props.fadeEdges }">
    <template v-if="hasContent">
      <div v-for="(rowItems, rowIndex) in rowsData"
           :key="rowIndex"
           class="scroll-row-wrapper mb-4 last:mb-0"
           :class="{ 'pause-on-hover': props.pauseOnHover }">
        <div class="scroll-row-container flex flex-nowrap"
             :class="cn(
               'w-max',
               startAnimation ? 'animation-running' : 'animation-paused',
               rowIndex % 2 === 0 ? 'scroll-left-animation' : 'scroll-right-animation',
             )"
             :style="{ animationDuration: calculatedDurations[rowIndex] }">
          <div :ref="(el) => { if (el) contentRefs[rowIndex] = el as Element | ComponentPublicInstance }"
               class="scroll-content flex-none">
            <component :is="itemVNode"
                       v-for="(itemVNode, itemIndex) in rowItems"
                       :key="getVNodeKey(itemVNode, itemIndex)"
                       class="inline-block"/>
          </div>
          <div class="scroll-content flex-none" aria-hidden="true">
            <component :is="itemVNode"
                       v-for="(itemVNode, itemIndex) in rowItems"
                       :key="getVNodeCopyKey(itemVNode, itemIndex)"
                       class="inline-block"/>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped>
.scroll-outer-wrapper.fade-edges-active {
  mask-image: linear-gradient(to right,
  transparent 0%,
  black 10%,
  black 90%,
  transparent 100%);
}

@keyframes scroll-left {
  0% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-50%);
  }

  100% {
    transform: translateX(0%);
  }
}

.scroll-row-container {
  animation-play-state: paused;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.scroll-left-animation {
  animation-name: scroll-left;
}

.scroll-right-animation {
  animation-name: scroll-right;
}

.scroll-row-container.animation-running {
  animation-play-state: running;
}

.scroll-row-wrapper.pause-on-hover:hover .scroll-row-container.animation-running {
  animation-play-state: paused;
}

.scroll-row-wrapper.last\:mb-0:last-child {
  margin-bottom: 0;
}
</style>