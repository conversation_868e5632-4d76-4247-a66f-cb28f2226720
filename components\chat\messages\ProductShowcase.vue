<script setup lang="ts">
import type {ProductData} from '@/types/chat'

// 定义组件的props
const props = defineProps<{
    // 商品消息对象
    productInfo: ProductData
}>()
</script>

<template>
  <div class="bg-white dark:bg-slate-800 rounded-lg max-w-xs">
    <div class="flex items-center gap-3">
      <img :src="productInfo.picture" :alt="productInfo.title" class="w-16 h-16 rounded-md">
      <div class="flex flex-col justify-between flex-1">
        <a :href="productInfo.productUrl" target="_blank" class="hover:underline">
          <p class="font-semibold line-clamp-2 text-blue-500">
            {{ productInfo.title }}
          </p>
        </a>
      </div>
    </div>
    <div v-if="productInfo.itemList && productInfo.itemList.length > 0"
         class="mt-2 pt-2 border-t border-slate-200 dark:border-slate-700">
      <div v-for="item in productInfo.itemList" :key="item.skuId" class="text-sm text-muted-foreground mt-2 mb-2">
        <p v-if="item.skuTitle">
          规格: {{ item.skuTitle }}
        </p>
        <div class="flex justify-between items-center mt-1">
          <div>
            <span v-if="item.purchasePrice" class="text-red-500 font-bold">
              ¥{{ item.purchasePrice }}
            </span>
            <span v-if="item.originalPrice && item.purchasePrice && item.originalPrice !== item.purchasePrice"
                  class="text-xs text-muted-foreground line-through ml-2">
              ¥{{ item.originalPrice }}
            </span>
          </div>
          <span v-if="item.count" class="text-xs">数量: x{{ item.count }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
