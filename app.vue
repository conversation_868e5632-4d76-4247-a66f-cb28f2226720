<script setup lang="ts">
import {Toaster} from '@/components/ui/sonner'
import SupportFormIntegration from '@/components/common/SupportFormIntegration.vue'
import FullLoading from '~/components/common/FullLoading.vue'
import {RouteRegexConstant} from '~/utils/constants/regex'
import CaptchaDialog from '@/components/common/CaptchaDialog.vue'
import {isCaptchaDialogVisible} from '~/composables/useCaptcha'
import {projectConfig} from '~/config/projectConfig'

const nuxtApp = useNuxtApp()
const route = useRoute()

// 是否显示加载状态
const isFullLoading = ref(true)
// 记录上一个布局
const previousLayout = ref<string | undefined>('default')
// 是否处于布局变化状态
const isLayoutChanging = ref(false)

const unSetUrlList = ['/privacy-policy', '/terms-of-service']

const layout = computed((): string | undefined => {
    const {path} = route

    // 检查路径是否在 unSetUrlList 中，考虑语言代码的情况
    for (const url of unSetUrlList) {
        const pattern = new RegExp(`^(\\/[a-z]{2})?${url.replace(/\//g, '\\/')}$`)
        if (pattern.test(path)) {
            return undefined
        }
    }

    if (RouteRegexConstant.USER_DASHBOARD.test(path)) {
        return 'dashboard'
    }
    if (RouteRegexConstant.ADMIN_DASHBOARD.test(path)) {
        return 'admin-dashboard'
    }
    return 'default'
})

// 为 NuxtLayout 组件准备name属性，将undefined转换成false
const layoutNameForNuxtLayout = computed(() => {
    const lVal = layout.value
    if (lVal === undefined) {
        return false
    }
    return lVal
})

// 页面开始加载时（导航开始）
nuxtApp.hook('page:start', () => {
    isFullLoading.value = true
})

// 页面加载完成时（导航结束）
nuxtApp.hook('page:finish', () => {
    isFullLoading.value = false
    // 当页面加载完成，如果之前正在改变布局，也标记为改变完成
    if (isLayoutChanging.value) {
        isLayoutChanging.value = false
    }
})

// 监听布局变化
watch(layout, (newLayout: string | undefined) => {
    if (newLayout !== previousLayout.value) {
        isLayoutChanging.value = true
        previousLayout.value = newLayout
    }
})

// 当页面完成过渡动画时
nuxtApp.hook('page:transition:finish', () => {
    // 确保布局过渡完成后才隐藏加载状态
    // isLayoutChanging.value = false; // This line is now handled by page:finish, can be kept for safety or removed if page:finish is sufficient
    // 如果 page:finish 中的处理足够，可以注释或移除这一行，以避免潜在的冲突或冗余。
    // 但保留它通常是安全的，因为将 ref 设置为它已有的值不会产生副作用。
    // 为了更清晰地依赖 page:finish 处理加载状态，我们这里注释掉它。
})

// 在页面首次加载时，设置正确的初始布局
onMounted(() => {
    previousLayout.value = layout.value

    // 使用微任务队列确保 DOM 已更新
    nextTick(() => {
    // 延迟关闭初始加载状态，确保应用完全渲染
        setTimeout(() => {
            isFullLoading.value = false
        }, 300)
    })
})

</script>

<template>
  <div>
    <!-- 使用 FullLoading 组件显示加载状态，在页面初次加载或布局变化时显示 -->
    <FullLoading v-if="isFullLoading || isLayoutChanging" />

    <NuxtLayout :name="layoutNameForNuxtLayout as any">
      <NuxtPage />
    </NuxtLayout>
    <ClientOnly>
      <Toaster rich-colors
               close-button/>
    </ClientOnly>
    <SupportFormIntegration :request-id-editable="true" />

    <!-- 添加 CaptchaDialog 组件，并通过 v-model 控制 -->
    <CaptchaDialog v-if="projectConfig.captchaVerify.useDialog" v-model:model-value="isCaptchaDialogVisible"/>
    <div v-else id="captcha-box" class="min-h-[160px] flex items-center justify-center"/>

    <!-- 旧的 captcha-box 已移除 -->
  </div>
</template>

<style>
/* 页面过渡动画 - 渐变滑入效果 */
.page-enter-active,
.page-leave-active {
  transition: all 0.35s cubic-bezier(0.33, 1, 0.68, 1);
}

.page-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 布局过渡动画 - 温和的缩放淡入效果 */
.layout-enter-active,
.layout-leave-active {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  transition-property: opacity, transform;
}

.layout-enter-from {
  opacity: 0;
  transform: scale(0.98);
}

.layout-leave-to {
  opacity: 0;
  transform: scale(1.02);
}
</style>
