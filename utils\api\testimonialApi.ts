import {createCrudApi} from './crud'
import type {TestimonialResponse} from '@/types/api'

// 定义基础路径
const BASE_PATH = '/testimonial'

/**
 * 评论/推荐语相关的 API 服务
 *
 * 使用通用的 CRUD API 创建器生成基础的增删改查操作。
 * 如果需要特定的非 RESTful 操作，可以在下面单独添加。
 */
export const testimonialApi = {
    ...createCrudApi<TestimonialResponse>(BASE_PATH)

    // --- 特定 API ---
    // 如果有其他特定于评论的接口，可以在这里添加
    // 例如: 批准评论
    // approve: (id: string | number) => {
    //     return http.post(`${BASE_PATH}/${id}/approve`);
    // },

}