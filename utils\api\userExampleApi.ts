import {type ApiResponse, Get, Post} from '~/utils/http'
import {createCrudApi} from './crud'
import {logger} from '~/utils/logger'
import type {UserExampleParams, UserExampleResponse} from '~/types/api'

/**
 * 创建用户管理API
 */
function createUserApi() {
    // 基础URL
    const baseUrl = '/user'

    // 创建基础CRUD操作
    const userCrudApi = createCrudApi<UserExampleParams, UserExampleResponse>(baseUrl)

    /**
     * 重置用户密码（非RESTful风格）
     * @param id 用户ID
     * @param newPassword 新密码
     * @returns 重置结果
     */
    function resetPassword(id: string, newPassword: string): Promise<ApiResponse<null>> {
        logger.info('重置用户密码', {id})
        return Post<null>(`${baseUrl}/${id}/resetPassword`, {password: newPassword})
    }

    /**
     * 获取用户统计数据（非RESTful风格）
     * @param params 查询参数
     * @returns 用户统计数据
     */
    function getUserStats(params?: Partial<UserExampleParams>): Promise<ApiResponse<UserExampleResponse>> {
        logger.info('获取用户统计数据', {params})
        return Get<UserExampleResponse>(`${baseUrl}/stats`, params)
    }

    // 返回合并的API对象
    return {
        // 扩展基础CRUD操作
        ...userCrudApi,
        // 自定义方法
        resetPassword,
        getUserStats
    }
}

// 导出用户API
export const userExampleApi = createUserApi()