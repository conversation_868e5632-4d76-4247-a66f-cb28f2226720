<script setup lang="ts">
import {ChevronRight} from 'lucide-vue-next'

import {getLocalizedConfigText, resolveLocalePath} from '~/utils/i18n'
import type {NavLink} from '~/types/site/navLinks'
import type {DashboardBreadcrumbItem} from '~/types/ui'
import type {DashboardGroupMenu} from '~/types/site/dashboardMenuConfig'

const props = defineProps<{
    menuNavigation: DashboardGroupMenu[]
}>()

const route = useRoute()
const dashboardBreadcrumbsRef = useState<DashboardBreadcrumbItem[]>('dashboardBreadcrumbs')

function isActive(itemHref?: string, itemChildren?: NavLink[]): boolean {
    if (!itemHref && !itemChildren) {
        return false
    }

    if ((!itemHref || itemHref === '') && itemChildren) {
        return itemChildren.some(child => child.href && route.path === resolveLocalePath(child.href))
    }

    return !!(itemHref && route.path === resolveLocalePath(itemHref))
}

function calculateDefaultOpen(item: NavLink): boolean {
    if (isActive(item.href, item.children)) {
        return true
    }

    if (typeof item.defaultOpen === 'boolean') {
        return item.defaultOpen
    }
    return false
}

function handleNavLinkClick(group: DashboardGroupMenu, item: NavLink, subItem?: NavLink) {
    const newCrumbs: DashboardBreadcrumbItem[] = []
    if (group.label) {
        newCrumbs.push({text: getLocalizedConfigText(group.label)})
    }

    if (subItem && subItem.href) {
        newCrumbs.push({text: getLocalizedConfigText(item.title)})
        newCrumbs.push({text: getLocalizedConfigText(subItem.title), href: resolveLocalePath(subItem.href)})
    } else if (item.href) {
        newCrumbs.push({text: getLocalizedConfigText(item.title), href: resolveLocalePath(item.href)})
    }
    dashboardBreadcrumbsRef.value = newCrumbs
}
</script>

<template>
  <SidebarGroup v-for="(groupItem, groupIndex) in props.menuNavigation" :key="groupIndex">
    <SidebarGroupLabel v-if="groupItem.label">{{ getLocalizedConfigText(groupItem.label) }}</SidebarGroupLabel>
    <SidebarMenu>
      <template v-for="(item, menuIndex) in groupItem.menus" :key="`${menuIndex}-${item.href}`">
        <Collapsible v-if="item.children && item.children.length > 0"
                     as-child
                     :default-open="calculateDefaultOpen(item)"
                     class="group/collapsible">
          <SidebarMenuItem>
            <CollapsibleTrigger as-child>
              <SidebarMenuButton :tooltip="getLocalizedConfigText(item.title)">
                <component :is="item.icon" v-if="item.icon" class="h-4 w-4"/>
                <span>{{ getLocalizedConfigText(item.title) }}</span>
                <ChevronRight
                  class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"/>
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                <SidebarMenuSubItem v-for="(subItem, subIndex) in item.children" :key="subIndex">
                  <SidebarMenuSubButton as-child :is-active="isActive(subItem.href)">
                    <NuxtLink class="cursor-pointer"
                              :to="resolveLocalePath(subItem.href!)"
                              @click="handleNavLinkClick(groupItem, item, subItem)">
                      {{ getLocalizedConfigText(subItem.title) }}
                    </NuxtLink>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
        <SidebarMenuItem v-else>
          <NuxtLink class="w-full" :to="resolveLocalePath(item.href!)" @click="handleNavLinkClick(groupItem, item)">
            <SidebarMenuButton :tooltip="getLocalizedConfigText(item.title)"
                               :is-active="isActive(item.href)"
                               class="w-full cursor-pointer">
              <component :is="item.icon" v-if="item.icon" class="h-4 w-4"/>
              <span>{{ getLocalizedConfigText(item.title) }}</span>
            </SidebarMenuButton>
          </NuxtLink>
        </SidebarMenuItem>
      </template>
    </SidebarMenu>
  </SidebarGroup>
</template>
