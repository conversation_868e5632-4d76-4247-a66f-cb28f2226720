(function () {

    // ================================== 功能支持 ================================

    function logger(data, isError = false, optionalParams) {
        if (isError) {
            console.error('【WS】', data, optionalParams)
        } else {
            console.log('【WS】', data, optionalParams)
        }
    }

    const toolCallEventType = 'CUSTOMER_TOOL_CALL'
    const secret = 'c0c01573152719128ab11eefeea5d96f4f547da4735200f836933bad24b7bea64eec9ebf09c22dbbc05f69ceec89ec28f9de90161dfc609b6e854cc47daf40375d0fec540d96eb39440912e27d79e730242a750aa11ea9881d30c6ac9d207e7ecee427b3865609a1ee731736b95f3d25'
    let token, platformId
    const rpaApi = 'http://127.0.0.1:5000'
    const serverApi = 'http://127.0.0.1:8080/api'
    const websocketApi = 'ws://127.0.0.1:40900/customer/agent'
    const platformInfo = {
        name: '',
        platformType: 'QIAN_NIU',
        id: '',
        logo: ''
    }

    const currentLoginUserinfo = {
        displayName: '',
        nickname: '',
        userId: '',
        avatar: '',
        platformAccount: true
    }

    window.imsdk.invoke('im.login.GetCurrentLoginID').then(async result => {
        currentLoginUserinfo.displayName = result.result.display
        currentLoginUserinfo.nickname = result.result.nick
        currentLoginUserinfo.userId = result.result.targetId
        currentLoginUserinfo.avatar = result.result.portrait

        platformInfo.name = currentLoginUserinfo.nickname
        platformInfo.id = currentLoginUserinfo.userId
        platformInfo.logo = currentLoginUserinfo.avatar

        logger('当前登录用户信息, 正在获取token', false, currentLoginUserinfo)
        getRequestToken().then(result => {
            if (result.code === 200) {
                token = result.data.token
                platformId = result.data.platformId
                logger(`通过密钥已成功获取到登录token\nsecret: ${secret}\ntoken: ${token}\nplatformId: ${platformId}`)
            } else {
                alert(`执行登录失败 ${result.message}`)
            }
        })
    })

    // ============================================================================
    // Websocket支持
    // ============================================================================
    let websocketClient

    async function executeToolCall(method, data) {
        const toolRequest = data.data
        const toolParams = toolRequest.params
        const {name} = toolParams
        const methodArguments = toolParams.arguments
        logger(`正在执行工具 ${name}, 执行参数`, methodArguments)
        try {
            const result = await Promise.resolve(method(data, methodArguments))
            logger(`工具 ${name} 已执行成功, 执行结果`, false, result)
            sendWebsocketForwardRequest(createToolSuccessResponse(toolRequest.id, result), toolCallEventType, data.metadata, null, data.requestId)
        } catch (e) {
            const errorMessage = `执行工具 ${name} 失败: ${e.message || e}`
            logger(errorMessage, true, e)
            sendWebsocketForwardRequest(createToolErrorResponse(toolRequest.id, e), toolCallEventType, data.metadata, null, data.requestId)
        }
    }

    function createToolSuccessResponse(id, resultData) {
        return {
            id: id,
            result: {
                isError: false,
                content: [
                    {
                        type: 'json',
                        text: JSON.stringify(resultData)
                    }
                ],
                structuredContent: resultData
            }
        }
    }

    function createWebSocket() {

        const eventListenerMap = new Map
        let webSocket = undefined
        let heartbeatTestTimer = undefined
        let reconnectTimer = undefined
        // ws链接是否是明确关闭
        let isExplicitClose = false
        const heartbeatIntervalTime = 30000

        function registerEventCallback(method, eventType, requestId) {
            let eventMap = eventListenerMap.get(eventType)
            if (!eventMap) {
                eventMap = new Map
            }
            if (!requestId) {
                requestId = 'default'
            }
            eventMap.set(requestId, method)
            eventListenerMap.set(eventType, eventMap)
            logger(`事件eventType: ${eventType}, requestId: ${requestId} 的处理方法已注册成功`)
        }

        function registerToolCallEventCallback(method, toolMethodName, requestId) {
            let eventMap = eventListenerMap.get(`FORWARD__REQUEST${toolCallEventType}`)
            if (!eventMap) {
                eventMap = new Map
            }
            if (!requestId) {
                requestId = 'default'
            }

            requestId = `${toolMethodName}_${requestId}`
            eventMap.set(requestId, method)
            eventListenerMap.set(`FORWARD__REQUEST${toolCallEventType}`, eventMap)
            logger(`工具方法: ${toolMethodName}, requestId: ${requestId} 事件处理器已注册成功`)
        }

        function registerRequestForwardEventCallback(method, eventType, requestId) {
            eventType = `FORWARD__REQUEST${eventType}`
            registerEventCallback(method, eventType, requestId)
        }

        function registerResponseForwardEventCallback(method, eventType, requestId) {
            eventType = `FORWARD__RESPONSE${eventType}`
            registerEventCallback(method, eventType, requestId)
        }

        async function send(data, eventType, metadata, isForwardResponse, responseErrorMessage, requestId, retries = 3) {
            if (!eventType) {
                logger(`eventType为null, 发送 ${data} 失败`, true)
                return
            }

            if (!webSocket || webSocket.readyState !== webSocket.OPEN) {
                logger('连接已断开, 正在尝试重新连接...', true)
                if (retries > 0) {
                    try {
                        await open()
                        logger('重连成功，将重新发送数据')
                        await send(data, eventType, metadata, isForwardResponse, requestId, retries - 1)
                    } catch (e) {
                        logger('重连失败，发送失败。', true)
                    }
                } else {
                    logger('已达到最大重试次数，发送失败', true)
                }
                return
            }

            const param = {
                event: eventType,
                requestId: requestId,
                metadata: metadata,
                forwardResponse: isForwardResponse
            }
            if (isForwardResponse) {
                param.response = data
                if (!metadata || !metadata.forwardNode) {
                    logger('当前请求是一个响应转发, 但是metadata中尚未forwardNode, 无法转发此请求')
                    return
                }

                metadata.forwardMode = 1
                metadata.ignoreServerResponse = true
                metadata.forwardTargetNode = metadata.forwardNode

                if (responseErrorMessage) {
                    param.responseErrorMessage = responseErrorMessage
                }
            } else {
                param.body = data
            }
            webSocket.send(JSON.stringify(param))
            logger(`【${eventType}】WS数据已发送 requestId: ${requestId}, metadata: ${metadata}`)
        }

        function startHeartbeat() {
            logger('开始发送心跳')
            if (heartbeatTestTimer) {
                clearInterval(heartbeatTestTimer)
            }
            heartbeatTestTimer = setInterval(() => {
                logger('发送心跳包')
                send({}, 'HEART_BEAT', {ignoreServerResponse: true}).catch(() => logger('心跳发送失败', true))
            }, heartbeatIntervalTime)
        }

        function stopHeartbeat() {
            logger('停止发送心跳')
            if (heartbeatTestTimer) {
                clearInterval(heartbeatTestTimer)
                heartbeatTestTimer = undefined
            }
        }

        function open() {
            return new Promise((resolve, reject) => {
                if (webSocket && webSocket.readyState === webSocket.OPEN) {
                    logger('连接已存在, 无需重新打开')
                    resolve()
                    return
                }
                isExplicitClose = false
                if (reconnectTimer) {
                    clearTimeout(reconnectTimer)
                    reconnectTimer = undefined
                }

                const connectUrl = `${websocketApi}?token=${token}`
                webSocket = new WebSocket(connectUrl)
                webSocket.addEventListener('open', (event) => {
                    logger(`已成功连接到Im服务器 连接地址 --> ${connectUrl}`)
                    startHeartbeat()
                    execute('CUSTOM_OPEN', {}, event)
                    resolve(event)
                })

                webSocket.addEventListener('close', (event) => {
                    handleConnectionClosed(event)
                    reject(event)
                })

                webSocket.addEventListener('error', (event) => {
                    handleConnectionClosed(event)
                    reject(event)
                })

                webSocket.addEventListener('message', (event) => {
                    const {data} = event
                    if (!data) {
                        console.error('【WS】收到的数据为空')
                        return
                    }

                    let parsedData
                    try {
                        parsedData = JSON.parse(data)
                    } catch (e) {
                        console.error(`【WS】无法将数据${data} 反序列化为对象`, e)
                        return
                    }

                    let eventType = parsedData['eventType']
                    if ('ERROR' === eventType) {
                        const originalEventType = parsedData['originalEventType']
                        if (originalEventType) {
                            eventType = originalEventType
                        }

                        let errorNotice
                        if (parsedData.message) {
                            errorNotice = parsedData.message
                        } else {
                            errorNotice = parsedData
                        }
                        logger(`【WS】eventType: ${eventType}, error: ${errorNotice}`, true)
                    }
                    execute(eventType, parsedData, event)
                })
            })
        }

        function execute(eventType, data, event) {
            let {requestId} = data
            if (eventType.startsWith('FORWARD__REQUEST')) {
                requestId = 'default'
            }

            if (!requestId) {
                requestId = 'default'
            }
            const eventMap = eventListenerMap.get(eventType)
            if (eventMap) {
                let isToolCall = false
                if (eventType === `FORWARD__REQUEST${toolCallEventType}`) {
                    // 工具调用请求
                    if (data.code !== 200 || !data.data || !data.data.id) {
                        logger('工具调用请求缺失重要参数', true)
                        return
                    }

                    const mcpRequest = data.data
                    const {id} = mcpRequest
                    const toolParams = mcpRequest.params
                    if (!toolParams || !toolParams.name) {
                        logger(`工具调用请求缺失方法名, sessionId: ${id}`, true)
                        sendWebsocketForwardRequest(createToolErrorResponse(id, '缺失工具方法'), toolCallEventType, data.metadata, null, data.requestId)
                        return
                    }

                    const methodName = toolParams.name
                    requestId = `${methodName}_${requestId}`
                    isToolCall = true

                    if (!eventMap.get(requestId)) {
                        logger(`工具: ${methodName}在本地尚未注册 无法执行`)
                        sendWebsocketForwardRequest(createToolErrorResponse(id, '工具尚未注册'), toolCallEventType, data.metadata, null, data.requestId)
                        return
                    }
                }
                const method = eventMap.get(requestId)
                if (method) {
                    logger(`正在执行事件: ${eventType}, requestId: ${requestId} 的处理方法`, false, data)
                    if (isToolCall) {
                        executeToolCall(method, data)
                    } else {
                        method(data, event)
                    }
                } else {
                    logger(`本地未注册事件: ${eventType}, requestId: ${requestId} 的处理方法`, true, data)
                }
            } else {
                logger(`本地未注册事件: ${eventType} 的处理方法`, true, data)
            }
        }

        function close() {
            logger('手动执行close操作')
            isExplicitClose = true
            stopHeartbeat()
            if (reconnectTimer) {
                clearTimeout(reconnectTimer)
                reconnectTimer = undefined
            }
            if (webSocket) {
                webSocket.close()
            }
        }

        function handleConnectionClosed(event) {
            execute('CUSTOM_CLOSE', {}, event)
            stopHeartbeat()
            if (isExplicitClose) {
                logger('主动断开连接, 不进行重连')
                return
            }

            if (reconnectTimer) {
                clearTimeout(reconnectTimer)
            }

            reconnectTimer = setTimeout(() => {
                logger('正在尝试重新连接...')
                open().catch(() => {
                    // 重新连接失败会自动触发 close/error 事件，无需在此处理
                })
            }, 3000)
        }

        return {
            registerEventCallback,
            registerRequestForwardEventCallback,
            registerResponseForwardEventCallback,
            registerToolCallEventCallback,
            send,
            close,
            open
        }
    }

    setTimeout(() => {
        if (!websocketClient) {
            websocketClient = createWebSocket()
            websocketClient.open()
            executeRegisterMethod()
        }
    }, 3000)

    const sendWebsocketForwardRequest = (data, eventType, metadata, responseErrorMessage, requestId) => {
        executeSendWebsocketRequest(data, eventType, metadata, true, responseErrorMessage, requestId)
    }

    const sendWebsocketCommonRequest = (data, eventType, metadata, requestId) => {
        executeSendWebsocketRequest(data, eventType, metadata, null, null, requestId)
    }

    const sendWebsocketPlatformData = (data, eventType, metadata, requestId) => {
        const packagedData = {
            data: data,
            platformId: platformInfo.id,
            platformUserinfo: currentLoginUserinfo
        }
        executeSendWebsocketRequest(packagedData, eventType, metadata, null, null, requestId)
    }

    const executeSendWebsocketRequest = (data, eventType, metadata, isForwardResponse, responseErrorMessage, requestId) => {
        if (!websocketClient) {
            websocketClient = createWebSocket()
            executeRegisterMethod()
            websocketClient.open().then(r => {
                websocketClient.send(data, eventType, metadata, isForwardResponse, responseErrorMessage, requestId)
            })
            return
        }
        websocketClient.send(data, eventType, metadata, isForwardResponse, responseErrorMessage, requestId)
    }

    // ============================================================================
    // 通用工具
    // ============================================================================
    function formatDate(date) {
        const year = date.getFullYear()

        // getMonth() 返回的月份是从 0 开始的 (0-11)，所以需要 +1
        const month = (date.getMonth() + 1).toString().padStart(2, '0')

        const day = date.getDate().toString().padStart(2, '0')
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        const seconds = date.getSeconds().toString().padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    /**
     * 发送post请求
     * @param {object} payload 请求体
     * @param {string} url url
     * @returns {Promise<object>} 成功时，Promise会被resolve，失败时会被reject。
     */
    function sendPostRequest(payload, url) {
        return new Promise((resolve, reject) => {
            if (!url) {
                reject({code: 500, message: 'url不能为空!'})
                return
            }

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            }).then(response => {
                return response.json().catch(() => {
                    return {
                        code: 500,
                        message: `服务器响应异常，状态码: ${response.status}`
                    }
                })
            }).then(data => {
                resolve(data)
            }).catch(() => {
                // 网络问题
                resolve({
                    code: 500,
                    message: '网络请求失败，请检查您的连接'
                })
            })
        })
    }

    /**
     * 尝试将给定URL的图片转换为Base64编码。
     *
     * const imageSrc = "https://self-cdn.xcye.xyz/files/486855/8e5aa0cd-df9f-493c-a517-0799a6a2d4a7.png"
     *     imageToBase64(imageSrc)
     *         .then(base64String => {
     *             console.log('图片成功转换为Base64：');
     *             // 输出的base64字符串会很长，这里只打印前面一部分作为示例
     *             console.log(base64String.substring(0, 100) + '...');
     *         })
     *         .catch(error => {
     *             //
     *         });
     * @param {string} imageUrl 图片的URL地址
     * @returns {Promise<string>} 返回一个Promise，成功时会解析为图片的Base64字符串，失败时则会拒绝。
     */
    function imageToBase64(imageUrl) {
        return new Promise((resolve, reject) => {
            const img = new Image()

            // 对于跨域图片，需要设置crossOrigin属性。
            // 但加上通常是好的实践。如果环境将 'pic:' 协议视为跨源，这有助于避免污染canvas。
            img.crossOrigin = 'Anonymous'

            // 图片成功加载后的处理
            img.onload = () => {
                const canvas = document.createElement('canvas')
                canvas.width = img.width
                canvas.height = img.height
                const ctx = canvas.getContext('2d')
                if (!ctx) {
                    return reject(new Error('无法获取Canvas 2D上下文。'))
                }
                ctx.drawImage(img, 0, 0)
                try {
                    // 从canvas中获取base64编码的图片数据 // 也可以是 'image/jpeg' 等
                    const dataURL = canvas.toDataURL('image/png')
                    resolve(dataURL)
                } catch (e) {
                    // 如果canvas因为加载了跨域图片而被"污染"，调用 toDataURL 会抛出安全错误
                    reject(new Error('无法将图片转换为Base64，Canvas可能已被跨域图片污染。', {cause: e}))
                }
            }

            // 图片加载失败的处理
            img.onerror = (error) => {
                reject(new Error('从提供的URL加载图片失败。请检查该协议是否被当前环境支持。', {cause: error}))
            }

            // 设置图片源，开始加载
            img.src = imageUrl
        })
    }

    function getOriginalEventType(responseEventType) {
        if (responseEventType.startsWith('FORWARD__REQUEST')) {
            return responseEventType.substring('FORWARD__REQUEST'.length, responseEventType.length)
        }

        if (responseEventType.startsWith('FORWARD__RESPONSE')) {
            return responseEventType.substring('FORWARD__RESPONSE'.length, responseEventType.length)
        }
        return responseEventType
    }

    function createToolErrorResponse(id, errorMsg) {
        return {
            id: id,
            result: {
                isError: true,
                content: [
                    {
                        type: 'text',
                        text: errorMsg
                    }
                ]
            }
        }
    }

    // ============================================================================
    // 执行业务
    // ============================================================================

    const emojiList = [
        {
            'id': 'a266100968d42aeb6206bc1a0d429111',
            'shortCut': '/:^_^',
            'meaning': '微笑',
            'originalFile': '0.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'b7caa81422f374967d58d1818ad6e252',
            'shortCut': '/:^$^',
            'meaning': '害羞',
            'originalFile': '1.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '9e1f079082e7362aa6e0f51fb39b746c',
            'shortCut': '/:Q',
            'meaning': '吐舌头',
            'originalFile': '2.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '7c509972c90578b0ca1b5ebcc1f974c5',
            'shortCut': '/:815',
            'meaning': '偷笑',
            'originalFile': '3.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'a76dc1154dbdf7ac796ec10b23dfc14d',
            'shortCut': '/:809',
            'meaning': '爱慕',
            'originalFile': '4.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'cd01e27cc4df01bb81d457175882b2a6',
            'shortCut': '/:^O^',
            'meaning': '大笑',
            'originalFile': '5.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '81bb86a70a6374407fef0796feaaa436',
            'shortCut': '/:081',
            'meaning': '跳舞',
            'originalFile': '6.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '99fe6b118db8d7fa920b6b0c0b34571e',
            'shortCut': '/:087',
            'meaning': '飞吻',
            'originalFile': '7.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'b27a7bdc2c36e3af262e306e4f478d09',
            'shortCut': '/:086',
            'meaning': '安慰',
            'originalFile': '8.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '43f59477f8cbf8a0f252393b0b7caad1',
            'shortCut': '/:H',
            'meaning': '抱抱',
            'originalFile': '9.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'b1ad5173ebfdeee421a71753c5a306d1',
            'shortCut': '/:012',
            'meaning': '加油',
            'originalFile': '10.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '1f99b093f40969a9c3a408ea5776b0ce',
            'shortCut': '/:806',
            'meaning': '胜利',
            'originalFile': '11.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '313a01f7a0ea7cc089fbd66c52da531b',
            'shortCut': '/:b',
            'meaning': '强',
            'originalFile': '12.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '5e6149d08be3299670221033162956fb',
            'shortCut': '/:^x^',
            'meaning': '亲亲',
            'originalFile': '13.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '2e7ffc952b310690aae54cd7bd707780',
            'shortCut': '/:814',
            'meaning': '花痴',
            'originalFile': '14.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '43a583814bc4009ed1a216b8641d3b78',
            'shortCut': '/:^W^',
            'meaning': '露齿笑',
            'originalFile': '15.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '3fde3722a3d69312278d3b4bab47347f',
            'shortCut': '/:080',
            'meaning': '查找',
            'originalFile': '16.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '420404731ea78867b985816923191e24',
            'shortCut': '/:066',
            'meaning': '呼叫',
            'originalFile': '17.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'cb346db7a2f8dd904d74992ba5fdb20c',
            'shortCut': '/:807',
            'meaning': '算账',
            'originalFile': '18.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'd5319a5a31eafdd88c17f48b9422c0de',
            'shortCut': '/:805',
            'meaning': '财迷',
            'originalFile': '19.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '3bc78e52ee9303a457b446719de1fb2a',
            'shortCut': '/:071',
            'meaning': '打CALL',
            'originalFile': '20.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '9483bb63d6ec79fc11660cba58dd69a3',
            'shortCut': '/:072',
            'meaning': '鬼脸',
            'originalFile': '21.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'dc4a4214e48d6bf8fd8d0e41cf7cbd67',
            'shortCut': '/:065',
            'meaning': '天使',
            'originalFile': '22.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '15d6b8cb7660c82035e374f75b03da3d',
            'shortCut': '/:804',
            'meaning': '再见',
            'originalFile': '23.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '7c811aedc303a9cc3c577d597a16862d',
            'shortCut': '/:813',
            'meaning': '憨笑',
            'originalFile': '24.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'd3d313da3c09effbeed91a3d7a0a3921',
            'shortCut': '/:818',
            'meaning': '享受',
            'originalFile': '25.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '62e35fc04bd68a3c4edce298465f6b23',
            'shortCut': '/:015',
            'meaning': '期待',
            'originalFile': '26.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '82d8adc5838091788069eb2378864fc9',
            'shortCut': '/:084',
            'meaning': '呆若木鸡',
            'originalFile': '27.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '519497966356ab9105084a023055a8a7',
            'shortCut': '/:801',
            'meaning': '思考',
            'originalFile': '28.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'c775f979db87080d64c75418047fb2ca',
            'shortCut': '/:811',
            'meaning': '迷惑',
            'originalFile': '29.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'ca39107ed4ad0a5b7d93b4812e803edd',
            'shortCut': '/:?',
            'meaning': '疑问',
            'originalFile': '30.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '1a7fecb0efda99c34347866b4acb9eb2',
            'shortCut': '/:077',
            'meaning': '剁手',
            'originalFile': '31.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '430e9d320a68f6bd8ad67aad8540869c',
            'shortCut': '/:083',
            'meaning': '无聊',
            'originalFile': '32.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '25bfb5e2a1a402ec53617bbf8cde009f',
            'shortCut': '/:817',
            'meaning': '怀疑',
            'originalFile': '33.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '44f7765420caf2bf45ac0bdc0a753adf',
            'shortCut': '/:!',
            'meaning': '嘘',
            'originalFile': '34.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '297ccde0d32e8708552e379eee5fc102',
            'shortCut': '/:068',
            'meaning': '做错事',
            'originalFile': '35.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'd81610fb561a32373db495a77d054832',
            'shortCut': '/:079',
            'meaning': '不容易',
            'originalFile': '36.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'ccbff27566cc86b5365ee6b9d7981980',
            'shortCut': '/:028',
            'meaning': '感冒',
            'originalFile': '37.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'fc1a14e240e5929341df23e4b617bf7e',
            'shortCut': '/:026',
            'meaning': '尴尬',
            'originalFile': '38.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '2b0c62c0d034fd3b31183eeb9cfe7c38',
            'shortCut': '/:007',
            'meaning': '傻笑',
            'originalFile': '39.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'c4452d1ac96c6159b5d7d5eb58e6af4f',
            'shortCut': '/:816',
            'meaning': '不会吧',
            'originalFile': '40.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '80d2b8bca00b130f48dd3cd6af9386a5',
            'shortCut': '/:\'""',
            'meaning': '南',
            'originalFile': '41.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'a8921c2ac3bcd7225a1f34d8eb6c4f00',
            'shortCut': '/:802',
            'meaning': '流汗',
            'originalFile': '42.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '568de31a56b2b54093a2b2121f4e0b3c',
            'shortCut': '/:027',
            'meaning': '凄凉',
            'originalFile': '43.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '0597655e98d6862b9ed17ef635bf50db',
            'shortCut': '/:(Zz...)',
            'meaning': '困了',
            'originalFile': '44.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'c2c3da85d2ceabefd867f173ed709076',
            'shortCut': '/:*&*',
            'meaning': '晕',
            'originalFile': '45.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '00e574e09b066e22d7a5768852299c64',
            'shortCut': '/:810',
            'meaning': '忧伤',
            'originalFile': '46.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '43054267e6e711b5d40963c0aa0dcc4c',
            'shortCut': '/:>_<',
            'meaning': '委屈',
            'originalFile': '47.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'f2934575beb75bda221a78f6475adc19',
            'shortCut': '/:018',
            'meaning': '悲泣',
            'originalFile': '48.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'c8454cf520ff0d054b92ebab56f776e8',
            'shortCut': '/:>O<',
            'meaning': '大哭',
            'originalFile': '49.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'a18fdad0117793325473ed66eab30f5c',
            'shortCut': '/:020',
            'meaning': '痛哭',
            'originalFile': '50.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '733763cfae7e06f53aa6b483092ea3da',
            'shortCut': '/:044',
            'meaning': 'I服了U',
            'originalFile': '51.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '87d093b7475d83b253dbfba72c2f5dfb',
            'shortCut': '/:819',
            'meaning': '对不起',
            'originalFile': '52.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'bab2fccdfbafe7c1520e5667f3e88e1a',
            'shortCut': '/:085',
            'meaning': '心酸',
            'originalFile': '53.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'd22b97374dba718d17d3fec2cf747c80',
            'shortCut': '/:812',
            'meaning': '皱眉',
            'originalFile': '54.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'd7064146b03b15c0064b1811113fec60',
            'shortCut': '/:"',
            'meaning': '好累',
            'originalFile': '55.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '4b757b9c06a284e7e8ca98eae9c5a152',
            'shortCut': '/:>@<',
            'meaning': '吐',
            'originalFile': '57.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '2212b491267b8e470cb61236f4251275',
            'shortCut': '/:076',
            'meaning': '背',
            'originalFile': '58.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'dfb0781b5b22b1d18db93b938666f71f',
            'shortCut': '/:069',
            'meaning': '惊讶',
            'originalFile': '59.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '92f28a87b236b8388212c474d414198a',
            'shortCut': '/:O',
            'meaning': '惊愕',
            'originalFile': '60.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'cd8e56b3db1bfc909a8cbffc64840fa7',
            'shortCut': '/:067',
            'meaning': '闭嘴',
            'originalFile': '61.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '836559d44fdf3efadec7fc612379b541',
            'shortCut': '/:043',
            'meaning': '欠扁',
            'originalFile': '62.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'af673dbc55971b09d2de1aded0aa8d21',
            'shortCut': '/:P',
            'meaning': '鄙视你',
            'originalFile': '63.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'bf377710482542cf6c4a38421c0e905c',
            'shortCut': '/:808',
            'meaning': '大怒',
            'originalFile': '64.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'c13d969a2bdede00465932dfc9d36d9d',
            'shortCut': '/:>W<',
            'meaning': '生气',
            'originalFile': '65.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '1f19ad224800997fcc7021b58784196a',
            'shortCut': '/:073',
            'meaning': '财神',
            'originalFile': '66.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '9a498fbaff972c141b9ca2743cd7c624',
            'shortCut': '/:008',
            'meaning': '请喝茶',
            'originalFile': '67.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '689a350c0554d28c2403f1c3624e3bec',
            'shortCut': '/:803',
            'meaning': '恭喜发财',
            'originalFile': '68.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '340713a8356835b2abf2b8eb45a9c349',
            'shortCut': '/:074',
            'meaning': '小二',
            'originalFile': '69.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'fad3a7a80769bf3cc999f260a3d8571e',
            'shortCut': '/:O=O',
            'meaning': '老大',
            'originalFile': '70.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '85af07debad4c61f983149873586d19a',
            'shortCut': '/:036',
            'meaning': '邪恶',
            'originalFile': '71.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'f61dd740dc5dd56372262575959002f8',
            'shortCut': '/:039',
            'meaning': '单挑',
            'originalFile': '72.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'b8e27504e9ec8f199a662e360796ae9b',
            'shortCut': '/:045',
            'meaning': '机智',
            'originalFile': '73.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '8d909832f1eb7957331b9a333d6a1880',
            'shortCut': '/:046',
            'meaning': '隐形人',
            'originalFile': '74.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '182626bc1d52a50f2af1d06fa37b89cc',
            'shortCut': '/:048',
            'meaning': '炸弹',
            'originalFile': '75.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'fbbb275456749bf7dc6dede700502c32',
            'shortCut': '/:047',
            'meaning': '惊声尖叫',
            'originalFile': '76.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '7f0a0028ae94eef786aa23ab3d23d33b',
            'shortCut': '/:girl',
            'meaning': '惊艳',
            'originalFile': '77.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '91fc79756aed81afe9e9fa0f53e308bb',
            'shortCut': '/:man',
            'meaning': '围观',
            'originalFile': '78.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'd140d727404b49099b5c9bf67c2a80f6',
            'shortCut': '/:052',
            'meaning': '招财猫',
            'originalFile': '79.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'db647ef4649c02e4510e729b3dee8fc3',
            'shortCut': '/:(OK)',
            'meaning': '成交',
            'originalFile': '80.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'f3df51ebbd8b45ac03f9e7aa2dfe78da',
            'shortCut': '/:8*8',
            'meaning': '鼓掌',
            'originalFile': '81.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '030fdc3d3084630d5e722fc60e61d956',
            'shortCut': '/:)-(',
            'meaning': '握手',
            'originalFile': '82.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'c336b30c3650e1d637b1d3741df475da',
            'shortCut': '/:lip',
            'meaning': '红唇',
            'originalFile': '83.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '1786c833f725c302432bf47ba0726ad1',
            'shortCut': '/:-F',
            'meaning': '玫瑰',
            'originalFile': '84.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'dae8010659e38776c2253502edb31a02',
            'shortCut': '/:-W',
            'meaning': '残花',
            'originalFile': '85.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '93bb8ad85a094bf3a81725e5558fcef0',
            'shortCut': '/:Y',
            'meaning': '爱心',
            'originalFile': '86.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '789a33f092a99ecee5e3216774e40def',
            'shortCut': '/:qp',
            'meaning': '心碎',
            'originalFile': '87.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '27e74c7e2dc2144b1a84683bdf6ad908',
            'shortCut': '/:$',
            'meaning': '红包',
            'originalFile': '88.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'fcd415d837067b0b296a04d92cf5c1c2',
            'shortCut': '/:%',
            'meaning': '快递传送',
            'originalFile': '89.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '93d52de24e825dfdb0b2b1bf5702261a',
            'shortCut': '/:(&)',
            'meaning': '快递盒',
            'originalFile': '90.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '9ba9f2895fae4b4fa6843585d01b69a6',
            'shortCut': '/:@',
            'meaning': '收件',
            'originalFile': '91.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '8bc867c1af690e3e244f265ced7f93a7',
            'shortCut': '/:~B',
            'meaning': '电话',
            'originalFile': '92.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '6ea70aa447eefbcbcc451bffdee0af33',
            'shortCut': '/:U*U',
            'meaning': '举杯庆祝',
            'originalFile': '93.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '97a255e3420cf7c9e076cc444a3d8d99',
            'shortCut': '/:clock',
            'meaning': '时钟',
            'originalFile': '94.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '0543fdb34bc66e5b5dc4711f246c8811',
            'shortCut': '/:R',
            'meaning': '等待',
            'originalFile': '95.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'ed0e7be432cd75942be5784dd5c637d1',
            'shortCut': '/:C',
            'meaning': '很晚了',
            'originalFile': '96.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': 'b509aa95b342d410c88bf922baf5d694',
            'shortCut': '/:plane',
            'meaning': '飞机',
            'originalFile': '97.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        },
        {
            'id': '201fd5e23c6529633cabfbbd96e1a8c7',
            'shortCut': '/:075',
            'meaning': '支付宝',
            'originalFile': '98.png',
            'fixedFile': '',
            'groupName': 'SysEmotion',
            'type': 0
        }
    ]
    const emojiCodeMapping = new Map
    const emojiMeaningMapping = new Map

    emojiList.forEach(emoji => {
        emojiCodeMapping.set(emoji.shortCut, emoji)
        emojiMeaningMapping.set(emoji.meaning, emoji)
    })

    setTimeout(() => {
        createControlElement()
    }, 2000)

    const createControlElement = () => {
        const msgList = document.getElementById('J_msg_list')
        if (!msgList) {
            logger('Element with ID "J_msg_list" not found.', true)
            return
        }

        const aiControlHTML = `
            <div class="J_msg imui-msg-system">
                <span>零一AI客服~ 状态: <span id="customer-agent-status">连接中...</span></span>
                <button id="toggle-ai-btn" style="margin-left: 10px;" disabled>关闭自动回复</button>
            </div>
            <ul id="websocketBox"></ul>
        `
        msgList.insertAdjacentHTML('beforeend', aiControlHTML)

        const statusSpan = document.getElementById('customer-agent-status')
        if (statusSpan) {
            statusSpan.style.color = 'red'
            statusSpan.style.fontWeight = 'bold'
        }

        const closeButton = document.getElementById('toggle-ai-btn')
        if (closeButton) {
            Object.assign(closeButton.style, {
                backgroundColor: '#e9ecef',
                color: '#6c757d',
                border: '1px solid #ccc',
                borderRadius: '4px',
                cursor: 'not-allowed'
            })

            closeButton.addEventListener('click', () => {
                if (!websocketClient) {
                    return
                }

                const button = document.getElementById('toggle-ai-btn')
                if (button.textContent === '关闭自动回复') {
                    websocketClient.close()
                } else if (button.textContent === '重新连接') {
                    const statusSpan = document.getElementById('customer-agent-status')
                    if (statusSpan) {
                        statusSpan.textContent = '连接中...'
                    }
                    button.disabled = true
                    Object.assign(button.style, {
                        backgroundColor: '#e9ecef',
                        color: '#6c757d',
                        cursor: 'not-allowed'
                    })
                    websocketClient.open()
                }
            })
        }
    }

    // 状态控制
    const handleWsOpen = () => {
        const aiStatus = document.getElementById('customer-agent-status')
        if (aiStatus) {
            aiStatus.textContent = '已连接'
        }
        const toggleBtn = document.getElementById('toggle-ai-btn')
        if (toggleBtn) {
            toggleBtn.disabled = false
            toggleBtn.textContent = '关闭自动回复'
            Object.assign(toggleBtn.style, {
                backgroundColor: '#f0f0f0',
                color: '#000',
                cursor: 'pointer'
            })
        }
    }

    const handleWsClose = () => {
        const aiStatus = document.getElementById('customer-agent-status')
        if (aiStatus) {
            aiStatus.textContent = '已关闭'
        }
        const toggleBtn = document.getElementById('toggle-ai-btn')
        if (toggleBtn) {
            toggleBtn.disabled = false
            toggleBtn.textContent = '重新连接'
            Object.assign(toggleBtn.style, {
                backgroundColor: '#28a745',
                color: 'white',
                border: '1px solid #28a745',
                cursor: 'pointer'
            })
        }
    }

    /**
     * 从URL中提取商品ID
     * @param {string} urlString
     * @returns {string|null}
     */
    function getProductIdFromUrl(urlString) {
        if (!urlString) {
            return null
        }
        const match = urlString.match(/[?&]id=([^&]+)/)
        return match ? match[1] : null
    }

    /**
     * 创建一个 CustomerMessage 的基础结构
     * @param data 原始数据
     */
    function createBaseCustomerMessage(data) {
        const receiver = {
            displayName: data.toid.display,
            nickname: data.toid.nick,
            avatar: data.toid.portrait,
            userId: data.toid.targetId,
            platformAccount: data.toid.targetId === currentLoginUserinfo.userId
        }
        const sender = {
            displayName: data.fromid.display,
            nickname: data.fromid.nick,
            avatar: data.fromid.portrait,
            userId: data.fromid.targetId,
            platformAccount: data.fromid.targetId === currentLoginUserinfo.userId
        }

        let contactInfo
        if (receiver.platformAccount) {
            contactInfo = sender
        } else {
            contactInfo = receiver
        }

        // 判断是否存在回复消息
        let replyMessage
        if (data.ext && data.ext.templateDynamicData && data.ext.templateDynamicData.qt && !data.isPackageReply) {
            const replyData = data.ext.templateDynamicData.qt
            let toid
            if (replyData.sender === data.fromid.targetId) {
                toid = data.toid
            } else {
                toid = data.fromid
            }
            replyData.toid = toid
            replyData.sendTime = data.sendTime
            replyData.summary = replyData.cont
            replyData.sortTimeMicrosecond = data.sortTimeMicrosecond
            replyData.mcode = {
                messageId: replyData.mcode.messageId,
                // 错误的
                clientId: data.mcode.clientId
            }
            replyData.loginid = data.loginid
            replyData.cid = data.cid
            replyData.isPackageReply = true
            replyMessage = parseQianniuMessage(replyData)
        }
        return {
            conversationId: data.cid.ccode,
            sendTime: formatDate(new Date(parseInt(data.sendTime, 10))),
            sender: sender,
            receiver: receiver,
            contactInfo: contactInfo,
            loginUser: currentLoginUserinfo,
            extData: {},
            list: [],
            cursorData: {
                clientId: data.mcode.clientId,
                messageId: data.mcode.messageId,
                sendTime: data.sendTime,
                sortTimeMicrosecond: data.sortTimeMicrosecond
            },
            platformMessageId: data.mcode.messageId,
            platformId: platformId,
            replyMessage: replyMessage
        }
    }

    /**
     * 解析 templateId: 129 的消息 订单相关
     */
    function parseTemplate129(data) {
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.category = 'ORDER_CONSULT'
        const dynamicContentList = data.ext && data.ext.dynamic_msg_content
        if (!dynamicContentList || dynamicContentList.length === 0) {
            return null
        }

        const subTemplateId = dynamicContentList[0].templateId
        const details = []

        switch (subTemplateId) {
            // 邀请下单
            case 399001:
                customerMessage.category = 'INVITE_ORDER'
                const templateData399001 = dynamicContentList[0].templateData
                const itemList399001 = templateData399001.itemList && templateData399001.itemList
                if (!itemList399001) {
                    return null
                }
                const productItemList399001 = itemList399001.map(item => {
                    return {
                        skuId: null,
                        skuTitle: item.itemSku.split(':')[1],
                        originalPrice: item.underlinedPrice && item.underlinedPrice !== '' ? parseFloat(item.underlinedPrice) : parseFloat(item.itemPrice),
                        purchasePrice: parseFloat(item.itemPrice),
                        count: parseInt(item.itemCount.substring(1), 10)
                    }
                }).filter(Boolean)
                const summary399001 = productItemList399001.map(item => item.skuTitle).filter(Boolean).join(', ')
                details.push({
                    type: 'INVITE_ORDER',
                    data: {
                        product: {
                            title: itemList399001[0].itemTitle,
                            productUrl: itemList399001[0].actionUrl,
                            id: getProductIdFromUrl(itemList399001[0].actionUrl),
                            picture: itemList399001[0].itemImgUrl,
                            itemList: productItemList399001
                        },
                        count: productItemList399001.map(t => t.count).filter(Boolean).reduce((sum, f) => {
                            return sum + f
                        }, 0)
                    },
                    messageContent: `亲，商品【${itemList399001[0].itemTitle}】中的【${summary399001}】已挑好，可直接购买哦`,
                    url: itemList399001[0].actionUrl
                })
                break
            // 用户来自于商品详情
            case 332001:
                customerMessage.category = 'PRODUCT_CONSULT'
                const templateData332001 = dynamicContentList[0].templateData
                const itemList332001 = templateData332001.itemList && templateData332001.itemList
                if (!itemList332001) {
                    return null
                }
                const productItemList332001 = itemList332001.map(item => {
                    return {
                        skuId: null,
                        skuTitle: null,
                        originalPrice: parseFloat(item.price),
                        purchasePrice: parseFloat(item.price),
                        count: 1
                    }
                }).filter(Boolean)
                details.push({
                    type: 'PRODUCT_CONSULT',
                    data: {
                        title: itemList332001[0].title,
                        productUrl: templateData332001.bottomClickUrl,
                        id: getProductIdFromUrl(templateData332001.bottomClickUrl),
                        picture: itemList332001[0].url,
                        itemList: productItemList332001
                    },
                    messageContent: `当前用户来自【${itemList332001[0].title}】商品详情页`,
                    url: templateData332001.bottomClickUrl
                })
                break
            case 200010:
                const templateData200010 = dynamicContentList[0].templateData
                const itemList200010 = templateData200010.E2_items && templateData200010.E2_items
                if (!itemList200010) {
                    return null
                }
                const skuList200010 = itemList200010.map(t => {
                    return {
                        skuId: null,
                        skuTitle: null,
                        originalPrice: parseFloat(t.price),
                        purchasePrice: parseFloat(t.price),
                        count: 1
                    }
                }).filter(Boolean)
                const productOrder200010 = {
                    product: {
                        title: itemList200010[0].title,
                        productUrl: itemList200010[0].actionUrl,
                        id: itemList200010[0].itemId,
                        picture: itemList200010[0].pic,
                        itemList: skuList200010
                    },
                    createTime: undefined,
                    status: undefined,
                    orderDetailUrl: undefined,
                    totalPrice: skuList200010.reduce((sum, f) => {
                        return sum + f.purchasePrice
                    }, 0.0),
                    count: skuList200010.map(t => t.count).filter(Boolean).reduce((sum, f) => {
                        return sum + f
                    }, 0),
                    orderExistStatus: false
                }
                details.push({
                    type: 'INVITE_ORDER',
                    data: productOrder200010,
                    messageContent: `亲，商品【${itemList200010[0].title}】的库存不多啦，喜欢就不要错过哦！`,
                    url: itemList200010[0].actionUrl
                })
                break
            // 咨询订单
            case 164002:
                const {templateData} = dynamicContentList[0]
                const orderInfo = {}
                templateData.E3_keyValueDescArray.forEach(kv => {
                    orderInfo[kv.key] = kv.desc
                })
                const productOrder = {
                    product: {
                        title: templateData.E2_title,
                        productUrl: null,
                        id: null,
                        picture: templateData.E2_pic,
                        itemList: [{
                            originalPrice: null,
                            purchasePrice: null,
                            count: parseInt(templateData.E2_content.replace(/[^0-9]/g, ''), 10) || 1
                        }]
                    },
                    createTime: new Date(orderInfo['创建时间']),
                    status: undefined,
                    orderDetailUrl: templateData.E4_buttonAction,
                    totalPrice: parseFloat(templateData.E2_price),
                    count: parseInt(templateData.E2_content.replace(/[^0-9]/g, ''), 10) || 1,
                    orderExistStatus: true,
                    orderId: orderInfo['订单号']
                }
                details.push({
                    type: 'ORDER_QUESTION',
                    data: productOrder,
                    messageContent: data.summary,
                    url: templateData.E4_buttonAction
                })
                break
            // 客服申请的退款申请已过期
            case 217002:
                const refundTemplateData = dynamicContentList[0].templateData
                const refundInfo = {}
                refundTemplateData.E2_keyValueDescArray.forEach(kv => {
                    refundInfo[kv.key] = kv.desc
                })
                const refundAmountStr = (refundInfo['退款金额'] || '').match(/￥([\d.]+)/)
                const refundAmount = refundAmountStr ? parseFloat(refundAmountStr[1]) : 0
                const productOrderRefund = {
                    product: {
                        title: refundTemplateData.E1_title,
                        productUrl: refundTemplateData.E1_actionUrl,
                        skuTitle: refundTemplateData.E1_subTitle,
                        price: refundAmount,
                        id: null, skuId: null,
                        picture: refundTemplateData.E1_pic
                    },
                    status: 'REFUND_EXPIRED',
                    totalPrice: refundAmount,
                    count: 1,
                    orderExistStatus: true
                }
                details.push({
                    type: 'ORDER_QUESTION',
                    data: productOrderRefund,
                    messageContent: `${refundTemplateData.E0_text}: ${refundTemplateData.E1_title}`
                })
                break
            case 251003:
                const couponTemplateData = dynamicContentList[0].templateData
                details.push({
                    type: 'TEXT',
                    data: `[优惠券] ${couponTemplateData.E0_title}`,
                    messageContent: `[优惠券] ${couponTemplateData.E0_title}`,
                    url: couponTemplateData.E0_actionUrl
                })
                break
            default:
                return packageUnknownMessage(`mainTemplate: 129, subTemplateId:${subTemplateId}`, data, customerMessage)
        }
        customerMessage.list = details
        return customerMessage
    }

    /**
     * 解析 templateId: 101 的消息 (文本、链接、表情、商品)
     */
    function parseTemplate101(data) {
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.category = 'TEXT'
        const jsviewList = data.originalData.jsview

        if (!jsviewList || jsviewList.length === 0) {
            // 处理没有 jsview 但有文本的简单消息
            if (data.originalData.text) {
                customerMessage.list.push({
                    type: 'TEXT',
                    data: data.originalData.text,
                    messageContent: data.originalData.text,
                    url: null
                })
                return customerMessage
            }
            return null
        }

        const details = jsviewList.map(jsview => {
            switch (jsview.type) {
                case 5: {
                    customerMessage.category = 'PRODUCT_CONSULT'
                    const urlInfo = JSON.parse(jsview.value.urlinfo)
                    const productData = {
                        id: (data.ext?.bizDataExt?.extraParams?.item_num_id) || getProductIdFromUrl(jsview.value.url) || null,
                        title: urlInfo.title,
                        productUrl: jsview.value.url,
                        picture: urlInfo.imageUrl,
                        itemList: [{
                            skuTitle: null,
                            skuId: null,
                            originalPrice: parseFloat(urlInfo.price),
                            purchasePrice: parseFloat(urlInfo.price),
                            count: urlInfo.sales
                        }]
                    }
                    return {
                        type: 'PRODUCT_CONSULT',
                        data: productData,
                        messageContent: urlInfo.title || jsview.value.url,
                        url: jsview.value.url
                    }
                }
                case 0:
                    const {text} = jsview.value
                    return {
                        type: 'TEXT',
                        data: text,
                        messageContent: text,
                        url: null
                    }
                case 3: { // 发送表情
                    const text = jsview.value.shortcut
                    return {
                        type: 'EMOJI',
                        data: {
                            url: jsview.value.pic,
                            shortCut: text,
                            meaning: emojiCodeMapping.get(text).meaning
                        },
                        messageContent: `[${emojiCodeMapping.get(text).meaning}]`,
                        url: null
                    }
                }
                case 1: { // 发送链接
                    return {
                        type: 'TEXT',
                        data: jsview.value.url,
                        messageContent: jsview.value.url,
                        url: jsview.value.url
                    }
                }
                default:
                    return packageUnknownMessage(`mainTemplate: ${101} subTemplateId: ${jsview.type}`, data, customerMessage)
            }
        }).filter(Boolean)

        if (details.length === 0) {
            return null
        }

        customerMessage.list = details
        return customerMessage
    }

    /**
     * 解析 templateId: 102 (图片)
     */
    function parseImageMessage(data) {
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.category = 'FILE'
        const fileData = {
            fileType: 'IMAGE',
            width: data.originalData.width,
            height: data.originalData.height,
            size: data.originalData.size,
            filename: data.originalData.fileId,
            url: data.originalData.url,
            file: null
        }
        customerMessage.list.push({type: 'FILE', data: fileData, messageContent: data.summary})
        return customerMessage
    }

    /**
     * 解析 templateId: 107 (文件)
     */
    function parseFileMessage(data) {
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.category = 'FILE'
        const fileMeta = data.originalData.fileMeta || data.originalData.jsFileInfo
        const fileData = {
            fileType: 'FILE',
            size: parseInt(fileMeta.nodeSize, 10),
            filename: fileMeta.nodeName,
            url: data.originalData.remotePath,
            file: null
        }
        customerMessage.list.push({type: 'FILE', data: fileData, messageContent: data.summary})
        return customerMessage
    }

    /**
     * 解析 templateId: 105 (视频)
     */
    function parseVideoMessage(data) {
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.category = 'FILE'
        const fileData = {
            fileType: 'VIDEO',
            width: data.originalData.width,
            height: data.originalData.height,
            size: data.originalData.size,
            filename: data.originalData.videoId,
            url: data.originalData.url,
            file: null,
            thumbnail: data.originalData.pic
        }
        customerMessage.list.push({type: 'FILE', data: fileData, messageContent: data.summary})
        return customerMessage
    }

    /**
     * 解析 templateId: 104 (语音)
     */
    function parseAudioMessage(data) {
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.category = 'FILE'
        const fileData = {
            fileType: 'AUDIO',
            size: data.originalData.size,
            filename: data.originalData.fileId,
            url: data.originalData.url,
            file: null
        }
        const messageContent = (data.ext && data.ext.bizDataExt && data.ext.bizDataExt.audio_text) || data.summary
        customerMessage.list.push({type: 'FILE', data: fileData, messageContent: messageContent})
        return customerMessage
    }

    /**
     * 解析 templateId: 116 (位置)
     */
    function parseLocationMessage(data) {
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.category = 'LOCATION'
        const locationData = {
            location: data.originalData.locationName,
            latitude: data.originalData.latitude,
            longitude: data.originalData.longitude
        }
        customerMessage.list.push({
            type: 'LOCATION',
            data: locationData,
            messageContent: data.originalData.locationName
        })
        return customerMessage
    }

    /**
     * 解析 templateId: 357002
     */
    function parseConsultSkuMessage(data) {
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.category = 'PRODUCT_CONSULT'
        const templateData = data.originalData
        const materialSummary = JSON.parse(data.ext.bizDataExt.template_material_summary)
        const productId = (materialSummary ? materialSummary.itemId : null) || getProductIdFromUrl(templateData.itemActionUrl)
        const productData = {
            id: productId,
            title: templateData.itemTitle,
            productUrl: `http://item.taobao.com/item.htm?id=${productId}`,
            picture: templateData.itemImgUrl,
            itemList: [{
                skuId: materialSummary ? materialSummary.skuId : null,
                skuTitle: templateData.itemSku,
                originalPrice: parseFloat(templateData.itemPrice),
                purchasePrice: parseFloat(templateData.itemPrice)
            }]
        }
        customerMessage.list.push({
            type: 'PRODUCT_CONSULT',
            data: productData,
            messageContent: `我要咨询商品 【${templateData.itemTitle}】的这个规格【${templateData.itemSku}】`
        })
        return customerMessage
    }

    /**
     * 解析 templateId: 287004
     */
    function parseSellerRefundMessage(data) {
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.category = 'ORDER_CONSULT'
        const templateData = data.originalData
        const picItem = templateData.E2_picItem && templateData.E2_picItem[0]
        const productOrder = {
            product: {
                title: picItem.title,
                productUrl: null, // not provided
                skuTitle: picItem.sku,
                price: parseFloat(templateData.E2_price),
                id: null, skuId: null,
                picture: picItem.picUrl
            },
            // TODO
            status: '',
            orderDetailUrl: templateData.E3_btnUrl,
            totalPrice: parseFloat(templateData.E2_price),
            count: parseInt(templateData.E2_count.replace(/[^0-9]/g, ''), 10) || 1
        }
        const messageContent = `${templateData.title}: ${templateData.E0_desc}`
        customerMessage.list.push({type: 'ORDER_QUESTION', data: productOrder, messageContent: messageContent})
        return customerMessage
    }

    /**
     * 解析暂未支持的消息
     */
    function parseUnknownMessage(data) {
        const message = data.summary
        const customerMessage = createBaseCustomerMessage(data)
        customerMessage.list.push({type: 'UNKNOWN', data: {type: data.templateId}, messageContent: message})
        return customerMessage
    }

    function packageUnknownMessage(templateId, data, customerMessage) {
        customerMessage.category = undefined
        const message = data.summary
        customerMessage.list.push({type: 'UNKNOWN', data: {type: templateId}, messageContent: message})
        return customerMessage
    }

    function recheckAndReedit(receiver, callback, fetchCount = 5) {
        if (fetchCount < 0) {
            // 重试多次都没找到这个用户
            logger(`当前已重试50次, 尚未找到用户 ${receiver.nickname}, 该消息将被丢弃`)
            return
        }
        window.imsdk.invoke('im.uiutil.GetCurrentConversationID').then(function (conversation) {
            if (receiver.nickname === conversation.result.nick) {
                callback()
            } else {
                // 重定向到用户
                window.location.href = `aliim:sendmsg?touid=cntaobao${receiver.nickname}&uid=cntaobao${currentLoginUserinfo.nickname}`
                setTimeout(function () {
                    recheckAndReedit(receiver, callback, fetchCount - 1)
                }, 100)
            }
        })
    }

    const repackageMsgList = (list, conversationId, receiver, openReply, cursorData, request, extData) => {
        const newList = []
        if (!request.metadata) {
            request.metadata = {}
        }

        new Promise((resolve) => {
            if (!list || list.length === 0) {
                sendWebsocketForwardRequest(null, 'CUSTOMER_SEND_NEW_MSG', request.metadata, '消息不存在', request.requestId)
                return resolve([])
            }
            list.forEach((item, index) => {
                let msgType = item.type

                if ('EMOJI' !== msgType && 'TEXT' !== msgType || index === 0) {
                    newList.push(item)
                    if (index === list.length - 1) {
                        resolve(newList)
                    }
                    return
                }

                const lastItem = newList[newList.length - 1]
                const lastMsgType = lastItem.type

                if ('EMOJI' === msgType) {
                    if (!item.data) {
                        logger('回复数据中，emoji数据为空')
                        return
                    }
                    // 映射meaning
                    const emoji = emojiMeaningMapping.get(item.data.meaning)
                    if (!emoji) {
                        logger(`无法通过 ${item.data.meaning} 查询到对应的表情, 该部分将被丢弃`)
                        if (index === list.length - 1) {
                            resolve(newList)
                        }
                        return
                    }
                    item = {
                        type: 'TEXT',
                        data: emoji.shortCut
                    }
                    msgType = 'TEXT'
                }

                if (msgType === lastMsgType) {
                    // 同种类型
                    lastItem.data = `${lastItem.data} ${item.data}`
                } else {
                    newList.push(item)
                }
                if (index === list.length - 1) {
                    resolve(newList)
                }
            })
        }).then(replyList => {
            console.log('最终的回复内容列表', false, {replyList, openReply, cursorData, extData, conversationId})
            // 将list中的消息同类型整合在一起
            recheckAndReedit(receiver, async () => {
                const isQuoteReply = openReply && cursorData && cursorData.messageId && cursorData.clientId

                if (isQuoteReply) {
                    logger('该条消息需要打开回复, 回复的消息为', false, cursorData)
                    // 打开回复
                    window.imsdk.invoke('im.singlemsg.OpenQuoteReply', {
                        'ccode': conversationId,
                        'mcode': {
                            messageId: cursorData.messageId,
                            clientId: cursorData.clientId
                        }
                    })
                }

                try {
                    for (const item of replyList) {
                        const msgData = item.data
                        const msgType = item.type

                        switch (msgType) {
                            case 'TEXT':
                                window.imsdk.invoke('im.bizutil.ReEdit', {
                                    'text': msgData,
                                    'cid': conversationId,
                                    'channelType': 2
                                })
                                const response = await executeRpa('send')
                                if (response.code !== 200) {
                                    // 发送执行响应
                                    sendWebsocketForwardRequest(null, request.metadata.originalEventType, request.metadata, response.message, request.requestId)
                                } else {
                                    sendWebsocketForwardRequest(null, request.metadata.originalEventType, request.metadata, null, request.requestId)
                                }
                                break
                            case 'FILE':
                                logger('收到文件回复', false, item)
                                const {fileType} = msgData
                                if ('IMAGE' !== fileType) {
                                    logger('当前仅支持图片回复', false)
                                    continue
                                }

                                const fileResponse = await executeRpa('image', {
                                    contentB64: msgData.base64,
                                    filename: msgData.filename,
                                    autoClose: false
                                })
                                if (fileResponse.code !== 200) {
                                    // 发送执行响应
                                    sendWebsocketForwardRequest(null, request.metadata.originalEventType, request.metadata, fileResponse.message, request.requestId)
                                } else {
                                    sendWebsocketForwardRequest(null, request.metadata.originalEventType, request.metadata, null, request.requestId)
                                }
                                break
                            default:
                                logger(`收到尚未支持的消息类型 ${msgType}`, true)
                        }
                    }
                } catch (error) {
                    logger('发送消息序列时出错', true, error)
                } finally {
                    if (isQuoteReply) {
                        window.imsdk.invoke('im.singlemsg.CloseQuoteReply', {
                            'ccode': {
                                ccode: conversationId
                            }
                        })
                        logger('回复窗口已关闭')
                    }
                }
            })
        })
    }

    /**
     * 主解析函数
     * @param data
     * @returns {CustomerMessage|null}
     */
    function parseQianniuMessage(data) {
        const {templateId} = data

        switch (templateId) {
            case 129:
                return parseTemplate129(data)
            case 101:
                return parseTemplate101(data)
            case 102:
                return parseImageMessage(data)
            case 104:
                return parseAudioMessage(data)
            case 105:
                return parseVideoMessage(data)
            case 107:
                return parseFileMessage(data)
            case 116:
                return parseLocationMessage(data)
            case 357002:
                return parseConsultSkuMessage(data)
            case 287004:
                return parseSellerRefundMessage(data)
            default:
                return parseUnknownMessage(data)
        }
    }

    /**
     * 调用RPA接口执行操作。
     * @param {string} actionType 操作类型，可以是 'send', 'image', 'file'。
     * @param {object} [options] 包含操作所需参数的对象。
     * @param {string} [options.contentB64] 'image' 或 'file' 类型的Base64编码内容。
     * @param {string} [options.filename] 文件名（可选）。
     * @param {boolean} [options.autoClose=false] 操作后是否自动关闭。
     * @returns {Promise<object>} 成功时，Promise会被resolve，失败时会被reject。
     */
    function executeRpa(actionType, {contentB64, filename, autoClose = false} = {}) {
        const payload = {
            type: actionType,
            autoClose: autoClose
        }

        if (actionType === 'image' || actionType === 'file') {
            if (!contentB64) {
                return Promise.reject({code: 500, message: `'${actionType}' 类型需要 'contentB64' 参数`})
            }
            payload.data = contentB64
            if (filename) {
                payload.filename = filename
            }
        }

        return sendPostRequest(payload, `${rpaApi}/execute/rpa`)
    }

    /**
     * 调用RPA接口关闭聊天窗口。
     * @returns {Promise<object>} 成功时，Promise会被resolve，失败时会被reject。
     */
    function executeClose() {
        return sendPostRequest(null, `${rpaApi}/execute/close`)
    }

    function getRequestToken() {
        const params = {
            platformDescName: `淘宝-${currentLoginUserinfo.nickname}`,
            useForToolCall: false,
            secret,
            accountInfo: {
                accountId: currentLoginUserinfo.userId,
                nickname: currentLoginUserinfo.nickname,
                avatar: currentLoginUserinfo.avatar,
                displayName: currentLoginUserinfo.displayName
            }
        }

        return sendPostRequest(params, `${serverApi}/customer-agent/user/genToken`)
    }

    // ============================================================================
    // 执行SDK事件
    // ============================================================================

    window.imsdk.on('im.uiutil.onConversationChange', data => {
        logger('对话发生改变', false, data)
        // 执行初始化
        if (!websocketClient) {
            websocketClient = createWebSocket()
            websocketClient.open()
            executeRegisterMethod()
        }
    })

    // TODO 有bug
    // window.imsdk.on('im.singlemsg.onReceiveNewMsg', data => {
    //     logger('收到新的消息', false, data)
    //     data.forEach(t => {
    //         const {ccode} = t.cid
    //         window.imsdk.invoke('im.singlemsg.GetRemoteHisMsg', {
    //             'cid': {
    //                 'ccode': ccode
    //             },
    //             'gohistory': 1,
    //             'count': 1
    //         }).then(result => {
    //             const receivedMessage = result.result.msgs[0]
    //             const message = parseQianniuMessage(receivedMessage)
    //             sendWebsocketPlatformData(message, 'SEND_NEW_MSG', {forwardMode: 2})
    //         })
    //     })
    // })
    //
    // // 在发送新消息时触发
    // window.imsdk.on('im.singlemsg.onSendNewMsg', data => {
    //     logger('已发送新的消息', false, data)
    //     data.forEach(t => {
    //         const {ccode} = t.cid
    //         window.imsdk.invoke('im.singlemsg.GetRemoteHisMsg', {
    //             'cid': {
    //                 'ccode': ccode
    //             },
    //             'gohistory': 1,
    //             'count': 1
    //         }).then(result => {
    //             const receivedMessage = result.result.msgs[0]
    //             const message = parseQianniuMessage(receivedMessage)
    //             sendWebsocketPlatformData(message, 'SEND_NEW_MSG', {forwardMode: 2})
    //         })
    //     })
    // })

    // ============================================================================
    // 执行业务请求
    // ============================================================================

    // 处理新消息回复
    const handleMessageReply = (result) => {
        const replyMsg = result.data
        const {conversationId, receiver, category, extData, openReply, list, cursorData} = replyMsg
        repackageMsgList(list, conversationId, receiver, openReply, cursorData, result, extData)
    }

    // 执行来自后台的发送消息转发
    const executeSendNewMsgForward = (request) => {
        logger('收到发送消息转发请求', request)
        const requestBody = request.data
        const msgData = requestBody.data
        const msgList = msgData.list
        const {extData} = requestBody
        repackageMsgList(msgList, msgData.conversationId, msgData.receiver, msgData.openReply, msgData.cursorData, request, extData)
    }

    // 发送新消息
    const sendNewMessage = (data) => {

    }

    const queryHistoryMsgListForward = (data) => {
        const request = data.data
        const {conversationId, pageSize, messageSort, cursorData} = request
        executeQueryHistoryMsgList(conversationId, pageSize, messageSort, cursorData, data)
    }

    // 查询历史消息
    const executeQueryHistoryMsgList = (conversationId, pageSize, messageSort, cursorData, requestData) => {
        const queryParam = {
            count: 20,
            gohistory: 1,
            cid: {
                ccode: conversationId
            }
        }

        if (cursorData && typeof cursorData === 'object' && Object.keys(cursorData).length > 0) {
            if (!cursorData.messageId || !cursorData.clientId || !cursorData.sendTime || !cursorData.sortTimeMicrosecond) {
                logger('请求参数中的cursorData不合法')
                return
            }
            queryParam.cursor = {
                mcode: {
                    messageId: cursorData.messageId,
                    clientId: cursorData.clientId
                },
                sendTime: cursorData.sendTime,
                sortTimeMicrosecond: cursorData.sortTimeMicrosecond
            }
        }
        // 为了和千牛本地调用一样 pageSize只能是1或20
        if (pageSize && pageSize === 1) {
            queryParam.count = 1
        }

        if ('asc' === messageSort) {
            queryParam.gohistory = 0
        }

        logger('执行查询历史消息', queryParam)
        window.imsdk.invoke('im.singlemsg.GetRemoteHisMsg', queryParam).then(result => {
            const hasMore = result.result.hasMore === 1
            const msgList = result.result.msgs
            const pageResponse = {
                additionalData: {
                    hasMore: hasMore
                },
                result: [],
                pageSize: queryParam.count
            }
            msgList.forEach((item, index) => {
                const message = parseQianniuMessage(item)
                if (message) {
                    pageResponse.result.push(message)
                }
                if (index === msgList.length - 1) {
                    console.log(`消息长度 ${pageResponse.result.length}`)
                    // 执行发送
                    sendWebsocketForwardRequest(pageResponse, 'CUSTOMER_QUERY_HISTORY_MSG', requestData.metadata, null, requestData.requestId)
                }
            })
        }).catch(e => {
            console.log(e)
        })
    }

    // 执行通用工具调用
    const executeRecallMessage = (data, toolArguments) => {
        logger('正在撤回消息', data, toolArguments)
        const {cursorData, platformMessageId, conversationId} = toolArguments

        return new Promise((resolve, reject) => {
            window.imsdk.invoke('im.singlemsg.DoChatMsgWithdraw', {
                cid: {
                    ccode: conversationId
                },
                mcodes: [
                    {
                        messageId: cursorData.messageId,
                        clientId: cursorData.clientId
                    }
                ]
            }).then(result => {
                resolve(result)
            }).catch(e => {
                if (e.passthrough && e.passthrough.detail) {
                    const obj = JSON.parse(e.passthrough.detail)
                    if (obj.reason) {
                        reject(obj.reason)
                        return
                    }
                }
                reject({'message': '未知错误', result: e})
            })
        })
    }

    // ================================== 执行注册ws事件处理函数 ================================
    const executeRegisterMethod = () => {
        logger('正在开始注册event callbacks')
        websocketClient.registerEventCallback(handleMessageReply, 'CUSTOMER_SEND_NEW_MSG')

        websocketClient.registerRequestForwardEventCallback(queryHistoryMsgListForward, 'CUSTOMER_QUERY_HISTORY_MSG')
        websocketClient.registerRequestForwardEventCallback(executeSendNewMsgForward, 'CUSTOMER_SEND_NEW_MSG')

        websocketClient.registerToolCallEventCallback(executeRecallMessage, 'MESSAGE_RECALL_MESSAGE')

        websocketClient.registerEventCallback(handleWsOpen, 'CUSTOM_OPEN')
        websocketClient.registerEventCallback(handleWsClose, 'CUSTOM_CLOSE')
        logger('event callbacks注册完成')
    }
})()
