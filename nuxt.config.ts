// https://nuxt.com/docs/api/configuration/nuxt-config
import tailwindcss from '@tailwindcss/vite'
import {defineOrganization} from 'nuxt-schema-org/schema'

export default defineNuxtConfig({
    compatibilityDate: '2024-11-01',
    devtools: {
        enabled: true,

        timeline: {
            enabled: true
        }
    },
    experimental: {
        typedPages: true
    },
    app: {
        head: {
            script: [
                {src: '/js/tac/js/tac.min.js', async: true, defer: true}
                // {src: '/js/redditWidgets.js', async: true, defer: true}
            ],

            // 设置默认的fallback 标题
            title: 'This is default fallback title',

            // 设置默认的站点语言
            htmlAttrs: {
                lang: 'en'
            },
            link: [
                // 设置站点的icon
                {rel: 'icon', type: 'image/x-icon', href: '/favicon.ico'}
            ]
        },
        pageTransition: {
            name: 'page',
            mode: 'out-in',
            duration: 350,
            css: true,
            enterActiveClass: 'page-enter-active',
            leaveActiveClass: 'page-leave-active',
            enterFromClass: 'page-enter-from',
            leaveToClass: 'page-leave-to'
        },
        layoutTransition: {
            name: 'layout',
            mode: 'out-in',
            duration: 400,
            css: true,
            enterActiveClass: 'layout-enter-active',
            leaveActiveClass: 'layout-leave-active',
            enterFromClass: 'layout-enter-from',
            leaveToClass: 'layout-leave-to'
        }
    },
    runtimeConfig: {
        public: {
            apiBase: '',
            wsApiBase: 'ws://127.0.0.1:40900'
        }
    },
    css: [
        '~/assets/css/tailwind.css',
        '~/assets/css/scrollbar.css',
        '~/public/js/tac/css/tac.css'
    ],
    modules: [
        '@nuxt/icon',
        '@nuxt/eslint',
        '@pinia/nuxt',
        '@nuxtjs/i18n',
        'shadcn-nuxt',
        '@nuxtjs/color-mode',
        '@nuxtjs/robots',
        '@nuxtjs/sitemap',
        'nuxt-og-image',
        'nuxt-schema-org'
    ],
    vite: {
        plugins: [
            tailwindcss()
        ],
        server: {
            allowedHosts: ['nps.zerones.net', 'api.template.dev']

        }
    },
    shadcn: {
        /**
         * Prefix for all the imported component
         */
        prefix: '',
        /**
         * Directory that the component lives in.
         * @default "./components/ui"
         */
        componentDir: './components/ui'
    },
    icon: {
        mode: 'css',
        cssLayer: 'base'
    },
    i18n: {
        baseUrl: process.env.NUXT_SITE_URL,
        lazy: true,
        defaultLocale: 'en',
        locales: [
            {code: 'en', language: 'en-US', name: 'English', files: ['en-US.json']},
            {code: 'zh', language: 'zh-CN', name: '中文', files: ['zh-CN.json']}
        ],
        detectBrowserLanguage: {
            useCookie: true,
            cookieKey: 'i18n_redirected',
            cookieCrossOrigin: true,
            redirectOn: 'root'
        }
    },
    colorMode: {
        preference: 'system',
        fallback: 'light',
        hid: 'nuxt-color-mode-script',
        globalName: '__NUXT_COLOR_MODE__',
        componentName: 'ColorScheme',
        classPrefix: '',
        classSuffix: '',
        storage: 'localStorage',
        storageKey: 'nuxt-color-mode'
    },
    site: {
        // 控制是否允许被索引
        indexable: true,
        // 配置站点的url
        url: process.env.NUXT_SITE_URL,
        name: 'my Awesome website'
    },
    robots: {
        // 配置不允许索引的地址
        disallow: ['/dashboard', '/admin', '/privacy-policy', '/terms-of-service']

    },
    sitemap: {
        sources: [
            `${process.env.NUXT_PUBLIC_API_BASE}/plat/posts/getOtherSlugList`,
            `${process.env.NUXT_PUBLIC_API_BASE}/plat/posts/getPostSlugList`
        ],
        // modify the chunk size if you need
        defaultSitemapsChunkSize: 2000,
        sitemapsPathPrefix: '/sitemap',
        // 配置站点地图缓存时间 单位s 设置为0表示禁用
        cacheMaxAgeSeconds: 0
    },
    ogImage: {
        defaults: {
            // 设置渲染图像的方式
            renderer: 'satori',

            // 配置缓存时间 单位秒
            cacheMaxAgeSeconds: 60 * 60 * 24 * 7
        },
        // 配置渲染时的字体 谷歌
        fonts: [
            // 应用首次启动时会下载字体 格式为(字体:[样式]:fontWeightValue)
            'Noto+Sans+SC:400'
        ],
        // 使用谷歌字体镜像
        googleFontMirror: true
    },
    schemaOrg: {
        identity: defineOrganization({
            // 组织名字
            name: 'Tech Zerone',
            // 组织的logo
            logo: '/logo.png',

            // 同属于组织的其他地址
            sameAs: ['https://www.zerones.net', 'https://www.xcye.xyz']
        })
    }

})
