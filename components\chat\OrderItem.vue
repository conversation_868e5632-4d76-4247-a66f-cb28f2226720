<script setup lang="ts">
import {ref} from 'vue'
import {Badge} from '@/components/ui/badge'
import {Button} from '@/components/ui/button'
import {Icon} from '#components'
import logger from '@/utils/logger'

// 定义组件的props
const props = defineProps<{
    // 订单对象
    order: {
        // 订单ID
        id: string
        // 订单状态
        status: string
        // 状态对应的CSS类
        statusClass: string
        // 下单日期
        orderDate: string
        // 下单时间
        orderTime: string
        // 付款时间
        paymentTime: string | null
        // 总价
        totalPrice: string
        // 商品数量
        itemCount: number
        // 邮费
        postage: string
        // 优惠金额
        discount: string
        // 收货信息
        shippingInfo: string
        items: {
            // 商品图片URL
            imageUrl: string
            // 商品标题
            title: string
            // 实际价格
            price: string
            // 原价
            originalPrice: string
            // SKU信息
            sku: string
            // 商品ID
            id: string
            // 发货时间
            deliveryTime: string
        }[]
    }
}>()

// 控制订单详情是否展开，默认为非"已评价"和非"已关闭"状态时展开
const isExpanded = ref(props.order.status !== '已评价' && props.order.status !== '已关闭')
// 控制收货地址是否可见
const isAddressVisible = ref(false)

/**
 * 处理各种操作按钮点击事件
 * @param action - 操作类型
 * @param orderId - 订单ID
 * @param payload - 附加数据
 */
function handleAction(action: string, orderId: string, payload: any = null) {
    logger.info(`Action: ${action} on order ${orderId}`, payload)
}

/**
 * 获取脱敏后的收货地址
 * @param address - 原始收货地址
 */
const getMaskedAddress = (address: string) => {
    if (!address) {
        return ''
    }
    const parts = address.split(', ')
    if (parts.length > 2) {
        const name = parts[0]
        const phone = parts[1]
        const restOfAddress = parts.slice(2).join(', ')
        // 对电话和详细地址进行脱敏
        const maskedPhone = phone.length > 4 ? `${phone.substring(0, 3)}****${phone.substring(phone.length - 4)}` : phone
        return `${name}, ${maskedPhone}, ${restOfAddress.replace(/./g, '*')}`
    }
    // 如果格式不符，则全部脱敏
    return address.replace(/./g, '*')
}
</script>

<template>
  <div class="bg-white dark:bg-slate-800 rounded-lg border">
    <!-- Order Header -->
    <div class="p-3 flex justify-between items-center cursor-pointer" @click="isExpanded = !isExpanded">
      <div class="flex items-center gap-2 text-sm">
        <Badge :class="order.statusClass" class="text-xs font-semibold">
          {{ order.status }}
        </Badge>
        <span class="text-muted-foreground">{{ order.id }}</span>
        <button class="flex items-center gap-1 text-muted-foreground hover:text-primary"
                @click.stop="handleAction('copy_id', order.id)">
          <Icon name="lucide:copy" class="w-3 h-3"/>
        </button>
        <a href="#" class="flex items-center gap-1 text-muted-foreground hover:text-primary" @click.stop>
          <span>详情</span>
        </a>
      </div>
      <div class="flex items-center gap-2">
        <Icon name="lucide:chevron-down"
              class="h-5 w-5 text-muted-foreground transition-transform duration-200"
              :class="{ 'rotate-180': isExpanded }"/>
      </div>
    </div>

    <!-- Collapsible Content -->
    <div v-if="isExpanded" class="p-3 border-t">
      <div class="flex flex-wrap gap-2 mb-3">
        <Button variant="secondary" size="xs" @click="handleAction('view_logistics', order.id)">
          查看物流
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('small_collect', order.id)">
          小额收款
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('small_payment', order.id)">
          小额打款
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('quick_copy', order.id)">
          快捷复制
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('reissue', order.id)">
          补发
        </Button>
      </div>

      <div class="text-xs text-muted-foreground">
        <p>{{ order.orderDate }} {{ order.orderTime }} 下单
          <span v-if="order.paymentTime">, {{ order.orderDate }} {{ order.paymentTime }} 付款</span>
        </p>
      </div>

      <div class="border rounded-md p-3 mt-3">
        <div class="flex justify-between items-center text-sm mb-2">
          <p>
            订单总价: <span class="font-bold text-red-500">¥{{ order.totalPrice }}</span>
            (共{{ order.itemCount }}件, 含邮费{{ order.postage }}元)
          </p>
        </div>
        <div class="group relative">
          <p class="text-sm text-muted-foreground mb-3">
            收货信息: {{ isAddressVisible ? order.shippingInfo : getMaskedAddress(order.shippingInfo) }}
          </p>
          <div
            class="absolute top-0 right-0 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white dark:bg-slate-800 px-2 rounded">
            <button class="flex items-center gap-1 text-muted-foreground hover:text-primary"
                    @click.stop="handleAction('copy_address', order.id, order.shippingInfo)">
              <Icon name="lucide:copy" class="w-4 h-4"/>
            </button>
            <button class="flex items-center gap-1 text-muted-foreground hover:text-primary"
                    @click.stop="handleAction('check_order', order.id)">
              <span class="w-4 h-4 inline-flex items-center justify-center">核</span>
            </button>
            <button class="flex items-center gap-1 text-muted-foreground hover:text-primary"
                    @click.stop="isAddressVisible = !isAddressVisible">
              <Icon :name="isAddressVisible ? 'lucide:eye-off' : 'lucide:eye'" class="w-4 h-4"/>
            </button>
          </div>
        </div>

        <div v-for="item in order.items" :key="item.id" class="flex gap-3 pt-3 border-t first:border-t-0">
          <div class="relative w-20 h-20">
            <img :src="item.imageUrl" class="w-full h-full rounded object-cover" alt="product">
            <Badge class="absolute bottom-1 left-1" :class="order.statusClass">
              {{ order.status }}
            </Badge>
          </div>
          <div class="flex-1 text-sm">
            <p class="font-semibold mb-1">
              {{ item.title }}
            </p>
            <p class="text-muted-foreground">
              实收: <span class="text-red-500">¥{{ item.price }} * 1</span>
              <s class="ml-2">¥{{ item.originalPrice }}</s>
            </p>
            <p class="text-muted-foreground">
              SKU: {{ item.sku }}
            </p>
            <p class="text-muted-foreground">
              编号: {{ item.id }}
            </p>
            <p class="text-muted-foreground">
              发货时间: {{ item.deliveryTime }}
            </p>
          </div>
        </div>
      </div>

      <div class="flex flex-wrap gap-2 mt-3">
        <Button variant="secondary" size="xs" @click="handleAction('view_review', order.id)">
          查看评价
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('logistics_helper', order.id)">
          物流助手
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('initiate_exchange', order.id)">
          发起退换
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('help_refund', order.id)">
          帮他退款
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('view_price_protection', order.id)">
          查看价保
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('说明', order.id)">
          说明
        </Button>
        <Button variant="secondary" size="xs" @click="handleAction('open_after_sales', order.id)">
          开启售后入口
        </Button>
      </div>
    </div>
  </div>
</template>
