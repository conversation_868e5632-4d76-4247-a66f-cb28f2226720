<script setup lang="ts">
import type {ChatMessage, ProductData} from '@/types/chat'
import ProductShowcase from '~/components/chat/messages/ProductShowcase.vue'

// 定义组件的props
const props = defineProps<{
    // 商品消息对象
    message: ChatMessage
}>()

const productInfo = computed(() => props.message.clientData as ProductData)
</script>

<template>
  <div class="bg-white dark:bg-slate-800 rounded-lg  max-w-xs">
    <div class="text-xs text-muted-foreground border-l-2 border-slate-200 dark:border-slate-700 pl-2 mb-3">
      {{ message.list[0].messageContent }}
    </div>
    <div class="p-3">
      <product-showcase :product-info="productInfo"/>
    </div>
  </div>
</template>
