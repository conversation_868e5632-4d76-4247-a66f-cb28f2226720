<script setup lang="ts">
import navLinkData from '~/config/navLinks'
import siteConfigData from '~/config/site'
import {getLocalizedConfigText} from '~/utils/i18n'

// 定义导航链接数组
const navLinks = ref(navLinkData)
const siteConfig = ref(siteConfigData)

// 从配置中获取网站标题的本地化文本
const localizedSiteTitle = computed(() => getLocalizedConfigText(siteConfig.value.title, 'Site Title'))

</script>

<template>
  <div class="flex items-center h-full">
    <!-- Logo 和品牌 -->
    <div class="flex items-center mr-4 sm:mr-6 h-full">
      <slot name="nav-logo" :site-config="siteConfig">
        <NuxtLink :to="$localePath({ path: '/' })" class="flex items-center h-full text-primary">
          <img v-if="siteConfig.logo"
               :src="siteConfig.logo"
               alt="Logo"
               class="w-6 h-6 mr-2 rounded-sm">
          <span class="font-bold ml-2">{{ localizedSiteTitle }}</span>
        </NuxtLink>
      </slot>
    </div>

    <slot name="before-nav"/>

    <!-- 导航链接 - 只在非移动端显示 -->
    <slot name="nav-show" :nav-links="navLinks">
      <nav class="hidden md:flex md:space-x-4 lg:space-x-6 h-full items-center">
        <NuxtLink v-for="(link, index) in navLinks"
                  :key="index"
                  :to="$localePath({ path: link.href })"
                  class="flex items-center h-full">
          <Icon v-if="link.icon" :name="link.icon" class="h-4 w-4 mr-2"/>
          <span>{{ getLocalizedConfigText(link.title) }}</span>
        </NuxtLink>
      </nav>
    </slot>

    <slot name="after-nav"/>
  </div>
</template>

<style scoped></style>