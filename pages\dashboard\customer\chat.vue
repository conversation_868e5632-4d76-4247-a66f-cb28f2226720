<script setup lang="ts">
import ChatHeader from '@/components/chat/ChatHeader.vue'
import ContactList from '@/components/chat/ContactList.vue'
import ChatWindow from '@/components/chat/ChatWindow.vue'
import RightPanel from '@/components/chat/RightPanel.vue'

import { type ChatMessage, type Contact, platformNameMapping, type PlatformType } from '@/types/chat'

import { Check, ChevronsUpDown, GalleryVerticalEnd, PanelLeftOpen, PanelRightOpen } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '~/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar'
import { type Ref, watch } from 'vue'
import { CustomerAgentClientWsService } from '~/utils/api/ws/agentClient.service'
import { platformApi } from '~/utils/api/customer/platformApi'
import type { Platform, PlatformAccount, PlatformDetail } from '~/types/api/response/PlatformResponse'
import { formatDateStr } from '~/utils/typeof'
// 定义平台类型的排序顺序
const platformTypeOrder: Record<PlatformType, number> = {
  // 可以根据实际需求调整排序值
  QIAN_NIU: 1,
  PIN_DUO_DUO: 2,
  JD: 3,
  XIAN_YU: 4
}
// 平台数据
const platforms: Ref<PlatformDetail[]> = ref([])
// 平台子账号列表
const subAccounts: Ref<PlatformAccount[]> = ref([])
// 当前选中的子账号
const selectedSubAccount: Ref<PlatformAccount | null> = ref(null)
// Websocket API 实例
const agentClientApi = ref<CustomerAgentClientWsService | null>(null)

// 左侧面板是否可见
const isLeftPanelVisible = ref(true)
// 右侧面板是否可见
const isRightPanelVisible = ref(false)

// 当前活跃的联系人
const activeContact: Ref<Contact | null> = ref(null)
// 当前活跃的平台
const activePlatform = ref<Platform | null>(null)
const activePlatformDetail = ref<PlatformDetail | null>(null)

// 计算各个平台的未读消息状态
const shopUnreadStatus = computed(() => {
  const status: Record<string, boolean> = {}
  // for (const shop of allShops.value) {
  //     status[shop.contactId] = contacts.value.some(
  //         contact => contact.shopId === shop.contactId && contact.unread > 0
  //     )
  // }
  return status
})

const getPlatformName = computed(() => {
  return (type: PlatformType) => {
    return platformNameMapping[type].name
  }
})

// 监测当前点击的平台ID变化
watch(activePlatform, (newValue) => {
  // 当平台变化时，获取新平台的子账号列表
  if (newValue) {
    subAccounts.value = newValue.accountDetailList
  }
}, { immediate: true })

async function fetchPlatformData() {
  const { code, data } = await platformApi.getPlatformList()
  logger.info('平台账户列表', { data, responseCode: code })
  if (code === 200 && data) {
    // 根据platformTypeOrder进行排序
    data.sort((a, b) => {
      const orderA = platformTypeOrder[a.platformType] || 999
      const orderB = platformTypeOrder[b.platformType] || 999
      return orderA - orderB
    })
    platforms.value = data
    if (platforms.value.length > 0 && platforms.value[0].list.length > 0) {
      activePlatform.value = platforms.value[0].list[0]
      activePlatformDetail.value = platforms.value[0]
      // 获取初始平台的子账号
      subAccounts.value = platforms.value[0].list[0].accountDetailList

      if (subAccounts.value.length > 0) {
        selectedSubAccount.value = subAccounts.value[0]
      } else {
        selectedSubAccount.value = null
      }
    }
  }
}

function handleShopClick(shop: Platform, platform: PlatformDetail) {
  activePlatform.value = shop
  activePlatformDetail.value = platform
  selectedSubAccount.value = shop.accountDetailList ? shop.accountDetailList[0] : null
}

// 组件挂载后执行，用于获取初始数据
onMounted(() => {
  fetchPlatformData()

  agentClientApi.value = new CustomerAgentClientWsService()
  agentClientApi.value.init({ nodeName: 'mock-node-name' })
})

/**
 * 选择一个联系人作为当前活跃联系人
 * @param contact 要选择的联系人对象，或null
 */
function selectContact(contact: Contact | null) {
  activeContact.value = contact
  if (!contact) {
    return
  }
  // 将选中联系人的未读消息数清零, a temporary solution
  // const targetContact = contacts.value.find(c => c.contactId === contact.contactId)
  // if (targetContact) {
  //     targetContact.unread = 0
  // }
}

onBeforeUnmount(() => {
  agentClientApi.value?.close()
})

/**
 * 处理发送消息的逻辑
 * @param messageContent 消息内容
 * @param messageType 消息类型
 * @param extras 附加信息，如回复的消息
 */
async function handleSendMessage(messageContent: string, messageType: ChatMessage['type'], extras?: {
  replyTo?: ChatMessage | null
}) {
  // This logic should be moved to ChatWindow.vue
}

/**
 * 处理更新消息的逻辑（例如，消息状态变更）
 * @param updatedMessage 包含更新信息的消息对象
 */
function handleUpdateMessage(updatedMessage: Partial<ChatMessage> & { contactId: string | number }) {
  // This logic should be moved to ChatWindow.vue
}

/**
 * 处理删除消息的逻辑
 * @param messageId 要删除的消息ID
 */
function handleDeleteMessage(messageId: string | number) {
  // This logic should be moved to ChatWindow.vue
}

</script>

<template>

  <div class="flex h-[calc(99vh-var(--header-height))]">
    <!-- Left Panel -->
    <transition name="fade">
      <div v-if="isLeftPanelVisible" class="flex w-[23rem] flex-shrink-0">
        <!-- Platform List -->
        <div class="flex w-20 flex-shrink-0 flex-col items-center gap-4 overflow-y-auto border-r bg-background py-4">
          <!--展示平台和平台-->
          <div v-for="platform in platforms" :key="getPlatformName(platform.platformType)"
            class="flex w-full flex-col items-center gap-2">
            <h3 class="text-sm font-semibold text-red-500">
              {{ getPlatformName(platform.platformType) }}
            </h3>
            <div v-for="shop in platform.list" :key="shop?.id" class="flex w-full flex-col items-center p-1">
              <div class="relative flex cursor-pointer flex-col items-center gap-1 rounded-lg p-2"
                :class="{ 'bg-primary/10': activePlatform?.id === shop.id }" @click="handleShopClick(shop, platform)">
                <Avatar class="h-10 w-10">
                  <AvatarImage :src="String(shop?.logo)" :alt="shop.name" />
                  <AvatarFallback>{{ String(shop?.name).substring(0, 1) }}</AvatarFallback>
                </Avatar>
                <span v-if="shopUnreadStatus[shop.id]"
                  class="absolute right-1 top-1 h-3 w-3 rounded-full border-2 border-background bg-green-500" />
                <span class="w-16 truncate text-center text-xs"
                  :class="activePlatform?.id === shop.id ? 'text-primary' : 'text-muted-foreground'">{{
                    shop.name
                  }}</span>
              </div>
            </div>
            <div class="my-2 h-px w-3/4 bg-border" />
          </div>
        </div>
        <!-- Contact List Panel -->
        <div class="w-72 flex-shrink-0 border-r">
          <div class="flex h-full flex-col">
            <header class="space-y-4 p-4">
              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <Button variant="outline" class="h-auto w-full justify-between">
                    <div class="flex items-center gap-3">
                      <div
                        class="flex size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                        <GalleryVerticalEnd class="size-4" />
                      </div>
                      <div v-if="selectedSubAccount" class="flex flex-col items-start gap-0.5 leading-none">
                        <span class="font-semibold">{{ selectedSubAccount.nickname }}</span>
                        <span v-if="selectedSubAccount.disconnectTime">
                          最后在线: {{ formatDateStr(selectedSubAccount.disconnectTime) }}
                        </span>
                        <span v-else>
                          在线: {{ formatDateStr(selectedSubAccount.connectTime) }}
                        </span>
                      </div>
                      <div v-else class="flex flex-col items-start gap-0.5 leading-none">
                        <span class="font-semibold">无可用账号</span>
                        <span>-</span>
                      </div>
                    </div>
                    <ChevronsUpDown class="ml-auto size-4 text-muted-foreground" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent class="w-[--radix-dropdown-menu-trigger-width]" align="start">
                  <DropdownMenuItem v-for="account in subAccounts" :key="account.accountId"
                    @select="selectedSubAccount = account">
                    <div class="flex flex-col">
                      <span>{{ account.nickname }} ({{ account.accountId }})</span>
                      <small>{{ account.displayName }}</small>
                    </div>
                    <Check v-if="selectedSubAccount && account.accountId === selectedSubAccount.accountId"
                      class="ml-auto size-4" />
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </header>
            <div class="flex-1 overflow-y-auto px-4">
              <ContactList v-if="agentClientApi && selectedSubAccount && activePlatform" :active-contact="activeContact"
                :active-platform="activePlatform" :agent-client-api="agentClientApi as CustomerAgentClientWsService"
                :selected-sub-account="selectedSubAccount" @select-contact="selectContact" />
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Main Content Area -->
    <div class="flex flex-1 flex-col">
      <header class="flex h-[var(--header-height)] shrink-0 items-center gap-4 border-b bg-background px-4">
        <Button variant="ghost" size="icon" @click="isLeftPanelVisible = !isLeftPanelVisible">
          <PanelLeftOpen class="size-5" />
        </Button>
        <div class="text-sm">
          <ChatHeader v-if="agentClientApi && activePlatform"
            :agent-client-api="agentClientApi as CustomerAgentClientWsService"
            :selected-sub-account="selectedSubAccount" :active-platform="activePlatform" />
        </div>
        <div class="ml-auto">
          <Button variant="ghost" size="icon" @click="isRightPanelVisible = !isRightPanelVisible">
            <PanelRightOpen class="size-5" />
          </Button>
        </div>
      </header>
      <main class="flex-1 overflow-auto p-4">
        <ChatWindow v-if="activeContact && selectedSubAccount && agentClientApi" :active-contact="activeContact"
          :platform-info="activePlatform" :current-sub-account="selectedSubAccount"
          :platform-type="activePlatformDetail ? activePlatformDetail.platformType : null"
          :agent-client-api="agentClientApi as CustomerAgentClientWsService" @send-message="handleSendMessage"
          @update-message="handleUpdateMessage" @delete-message="handleDeleteMessage" />
        <div v-else class="flex items-center justify-center h-full">
          <p class="text-muted-foreground">
            选择一个会话开始聊天
          </p>
        </div>
      </main>
    </div>

    <!-- Right Panel -->
    <transition name="fade">
      <div v-if="isRightPanelVisible" class="w-110 flex-shrink-0 border-l">
        <div class="flex h-full flex-col">
          <header class="flex h-[var(--header-height)] items-center justify-between border-b p-4">
            <div class="flex items-center gap-3">
              <Avatar class="size-8">
                <AvatarImage
                  :src="activeContact && activeContact.contactDetail.avatar ? activeContact.contactDetail.avatar : ''"
                  alt="@radix-vue" />
                <AvatarFallback>
                  {{
                    activeContact && activeContact.contactDetail.displayName ?
                      activeContact.contactDetail.displayName.substring(0, 2) : '--'
                  }}
                </AvatarFallback>
              </Avatar>
              <div class="flex flex-col">
                <h1 class="text-lg font-bold">
                  {{
                    !activeContact ? '--' : activeContact.contactDetail.displayName ?
                      activeContact.contactDetail.displayName : activeContact.contactDetail.userId
                  }}
                </h1>
                <div class="flex items-center gap-2 text-xs text-muted-foreground">
                  <span class="flex items-center gap-1">
                    <span v-if="activeContact && activeContact.onlineStatus"
                      class="h-2 w-2 rounded-full bg-green-500" />
                    <span v-else class="h-2 w-2 rounded-full bg-red-500" />
                    {{ activeContact ? activeContact.onlineStatus ? '在线' : '离线' : '--' }}
                  </span>
                </div>
              </div>
            </div>
          </header>
          <div class="flex-1 space-y-6 overflow-y-auto p-4">
            <div class="space-y-3">
              <RightPanel />
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<style>
html,
body,
#__nuxt {
  height: 100%;
  overflow: hidden;
}

:root {
  --header-height: 4rem;
  /* 64px */
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease-out, width 0.2s ease-out;
  overflow: hidden;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  width: 0;
}
</style>
