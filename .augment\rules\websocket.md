---
type: manual
---

## 后端WebSocket接口调用流程（类与事件总线模式）

本文档为项目中的WebSocket通信提供 **正确** 的调用规范与最佳实践。它基于 **面向对象（类与继承）**
的设计模式，旨在为开发者提供一个结构清晰、可复用、可扩展的WebSocket架构。
本文档是项目WebSocket开发的黄金标准。

### 核心理念：服务实例、事件驱动与状态管理

我们不直接使用底层的WebSocket客户端，而是通过可被实例化的 **服务类 (Service Class)** 来进行交互。

1. **服务实例 (Service Instance)**：每个独立的WebSocket连接都由一个独立的服务实例来管理。你可以在需要时 `new XXService()`(
   XXService需继承自BaseWsApiService) 来创建一个全新的、隔离的通信实例。
2. **事件驱动 (Event-Driven)**：服务实例通过 `on()` 方法来订阅服务端推送的事件，从而实现事件驱动的通信。

这种模式的完整实现可参考：

- 服务实现: [chatExampleWs.service.ts](mdc:utils/api/example/ws/chatExampleWs.service.ts)
- 组件使用: [chatWebSocketExample.vue](mdc:utils/api/example/ws/chatWebSocketExample.vue)

---

### 架构分层

我们的WebSocket架构分为三层，职责清晰：

1. **底层客户端[websocketClient.ts](mdc:utils/http/websocketClient.ts)**: 提供了最基础的 `createWebSocket`
   工厂函数，负责原始的连接、心跳和数据收发。**通常业务开发者无需关心这一层**。
2. **通用服务基类[BaseWsApiService.class.ts](mdc:utils/http/BaseWsApiService.class.ts)**: 一个抽象基类，封装了所有服务实例通用的逻辑，如生命周期管理（
   `init`, `on`, `close`）等。
3. **具体业务服务 (`*.service.ts`)**: 继承自`BaseWsApiService`的子类，如`ChatWsService`。它只负责定义与自身业务相关的API方法（如
   `queryHistoryMsgList`）。

---

### 开发流程

#### 1. 定义类型

类型定义规范与HTTP接口完全一致。所有WebSocket相关的类型都应放在 `types/api` 目录下。

- **请求参数类型(当存在请求参数)**: 放在 `params` 目录，命名为 `实体名Params.ts`。
- **响应数据类型(当响应体data不为void)**: 放在 `response` 目录，命名为 `实体名Response.ts`。

#### 2. 实现业务服务类

当需要一个新的WebSocket功能时（例如“实时通知”），你应该创建一个新的服务类：

1. 在 `utils/api/ws` 目录下创建一个新文件，如 `notification.service.ts`。
2. 让你的新类继承 `BaseWsApiService`[BaseWsApiService.class.ts](mdc:utils/http/BaseWsApiService.class.ts)。
3. 在 `constructor` 中调用 `super()`，并传入该服务固定的`serverUri`。
4. 为你的业务添加公开方法，这些方法内部调用基类提供的 `this.send()` 来发送消息。

```typescript
// utils/api/notification.service.ts
import {BaseWsApiService} from '~/utils/http/BaseWsApiService.class';

export class NotificationWsService extends BaseWsApiService {
    constructor() {
        super({serverUri: '/notifications'});
    }

    public markAsRead(notificationId: string): void {
        this.send({id: notificationId}, 'MARK_AS_READ');
    }
}
```

#### 3. 在组件中使用服务

在Vue组件中使用服务实例的生命周期如下：

1. **创建实例**: 在组件挂载时（`onMounted`）或需要时，通过 `new` 关键字创建一个服务实例。
2. **初始化连接**: 调用实例的 `init()` 方法来建立WebSocket连接。
3. **订阅事件**: 立即使用实例的 `on()` 方法订阅你关心的服务端事件类型，并绑定处理函数。可以为同一个`eventType`根据不同的
   `requestId`注册多个处理器。
4. **调用方法**: 在用户的交互或其他时机，调用服务实例的业务方法（如`chatApi.queryHistoryMsgList(...)`）来向服务端发送消息。
5. **清理资源**: 在组件卸载前（`onBeforeUnmount`），**必须**调用 `close()` 关闭连接、清理资源。

完整的用法请参考 **[chatWebSocketExample.vue](mdc:utils/api/example/ws/chatWebSocketExample.vue)**。

```vue

<script setup lang="ts">
		import {ChatWsService} from '~/utils/api/example/ws/chatExampleWs.service';
		import type {HistoryMsg, WebSocketResponse} from '~/utils/http/types';
		
		const chatApi = ref<ChatWsService | null>(null);
		
		const handleHistoryMessages = (response: WebSocketResponse<HistoryMsg[]>) => {
				if (response.code === 200 && response.data) {
						// 推荐在这里调用Pinia Store来更新状态
						console.log('收到历史消息', response.data);
				}
		};
		
		onMounted(() => {
				// 1. 创建实例
				chatApi.value = new ChatWsService();
				// 2. 初始化连接
				chatApi.value.init({nodeName: 'unique-component-id'});
				// 3. 订阅事件
				chatApi.value.on<HistoryMsg[]>('QUERY_HISTORY_MSG_RESPONSE', handleHistoryMessages);
		});
		
		onBeforeUnmount(() => {
				if (chatApi.value) {
						// 5. 清理资源
						chatApi.value.close();
				}
		});
		
		function requestHistory() {
				// 4. 调用方法
				chatApi.value?.queryHistoryMsgList({contactId: '123', limit: 20});
		}
</script>
```
