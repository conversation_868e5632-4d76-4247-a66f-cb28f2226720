(function () {

    let websocketClient

    function logger(data, isError) {
        if (isError) {
            console.error('【WS】', data)
        } else {
            console.log('【WS】', data)
        }
    }

    function createWebSocket() {

        const eventListenerMap = new Map
        let webSocket = undefined
        let heartbeatTestTimer = undefined
        let reconnectTimer = undefined
        // ws链接是否是明确关闭
        let isExplicitClose = false
        const heartbeatIntervalTime = 30000

        function registerEventCallback(method, eventType, requestId) {
            let eventMap = eventListenerMap.get(eventType)
            if (!eventMap) {
                eventMap = new Map
            }
            if (!requestId) {
                requestId = 'default'
            }
            logger(`正在注册监听器 eventType: ${eventType}, requestId: ${requestId} --> ${method}`)
            eventMap.set(requestId, method)
            eventListenerMap.set(eventType, eventMap)
        }

        function registerForwardEventCallback(method, eventType, requestId) {
            eventType = `FORWARD__${eventType}`
            registerEventCallback(method, eventType, requestId)
        }

        async function send(data, eventType, requestId, retries = 3) {
            if (!eventType) {
                logger(`eventType为null, 发送 ${data} 失败`, true)
                return
            }

            if (!webSocket || webSocket.readyState !== webSocket.OPEN) {
                logger('连接已断开, 正在尝试重新连接...', true)
                if (retries > 0) {
                    try {
                        await open()
                        logger('重连成功，将重新发送数据')
                        await send(data, eventType, requestId, retries - 1)
                    } catch (e) {
                        logger('重连失败，发送失败。', true)
                    }
                } else {
                    logger('已达到最大重试次数，发送失败', true)
                }
                return
            }

            const param = {event: eventType, body: data, requestId: requestId}
            webSocket.send(JSON.stringify(param))
        }

        function execute(eventType, data, event) {
            let {requestId} = data
            if (!requestId) {
                requestId = 'default'
            }
            const eventMap = eventListenerMap.get(eventType)
            if (eventMap) {
                const method = eventMap.get(requestId)
                if (method) {
                    method(data, event)
                }
            }
        }

        function startHeartbeat() {
            logger('开始发送心跳')
            if (heartbeatTestTimer) {
                clearInterval(heartbeatTestTimer)
            }
            heartbeatTestTimer = setInterval(() => {
                logger('发送心跳包')
                send({message: 'ping'}, 'CUSTOM_HEARTBEAT').catch(() => logger('心跳发送失败', true))
            }, heartbeatIntervalTime)
        }

        function stopHeartbeat() {
            logger('停止发送心跳')
            if (heartbeatTestTimer) {
                clearInterval(heartbeatTestTimer)
                heartbeatTestTimer = undefined
            }
        }

        function open() {
            return new Promise((resolve, reject) => {
                if (webSocket && webSocket.readyState === webSocket.OPEN) {
                    logger('连接已存在, 无需重新打开')
                    resolve()
                    return
                }
                isExplicitClose = false
                if (reconnectTimer) {
                    clearTimeout(reconnectTimer)
                    reconnectTimer = undefined
                }

                const token = 'eyJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.d2UEaHOO0Q199sH__qzV7CPqCs0TQvGoKSvx5949M78'
                const serverUrl = 'ws://127.0.0.1:40900/customer/agent'
                const nodeName = '客户端-tool'
                const nodeType = 'CONTROLLER'
                const connectUrl = `${serverUrl}?nodeName=${nodeName}&nodeType=${nodeType}&token=${token}`
                webSocket = new WebSocket(connectUrl)
                webSocket.addEventListener('open', (event) => {
                    logger(`已成功连接到Im服务器 连接地址 --> ${connectUrl}`)
                    startHeartbeat()
                    execute('CUSTOM_OPEN', {}, event)
                    resolve(event)
                })

                webSocket.addEventListener('close', (event) => {
                    handleConnectionClosed(event)
                    reject(event)
                })

                webSocket.addEventListener('error', (event) => {
                    handleConnectionClosed(event)
                    reject(event)
                })

                webSocket.addEventListener('message', (event) => {
                    const {data} = event
                    if (!data) {
                        console.error('【WS】收到的数据为空')
                        return
                    }

                    let parsedData
                    try {
                        parsedData = JSON.parse(data)
                    } catch (e) {
                        console.error(`【WS】无法将数据${data} 反序列化为对象`, e)
                        return
                    }

                    let eventType = parsedData['eventType']
                    let originalEventType = undefined
                    if ('ERROR' === eventType) {
                        originalEventType = parsedData['originalEventType']
                        if (originalEventType) {
                            eventType = originalEventType
                        }

                        let errorNotice
                        if (parsedData.message) {
                            errorNotice = parsedData.message
                        } else {
                            errorNotice = parsedData
                        }
                        console.error(`【WS】eventType: ${eventType}, error: ${errorNotice}`)
                    }
                    execute(eventType, parsedData, event)
                })
            })
        }

        function close() {
            logger('手动执行close操作')
            isExplicitClose = true
            stopHeartbeat()
            if (reconnectTimer) {
                clearTimeout(reconnectTimer)
                reconnectTimer = undefined
            }
            if (webSocket) {
                webSocket.close()
            }
        }

        function handleConnectionClosed(event) {
            execute('CUSTOM_CLOSE', {}, event)
            stopHeartbeat()
            if (isExplicitClose) {
                logger('主动断开连接, 不进行重连')
                return
            }

            if (reconnectTimer) {
                clearTimeout(reconnectTimer)
            }

            reconnectTimer = setTimeout(() => {
                logger('正在尝试重新连接...')
                open().catch(() => {
                    // 重新连接失败会自动触发 close/error 事件，无需在此处理
                })
            }, 3000)
        }

        return {registerEventCallback, registerForwardEventCallback, send, close, open}
    }

    setTimeout(() => {
        websocketClient = createWebSocket()
        websocketClient.open()
        executeRegisterMethod()
    }, 3000)

    // ================================== 执行业务逻辑 =======================================

    // 处理新消息回复
    const newMessageReceive = (data) => {

    }

    // 发送新消息
    const sendNewMessage = (data) => {

    }

    // 查询历史消息
    const queryHistoryMsgListForward = (data) => {

    }

    // 执行通用工具调用
    const executeCommonToolCallForward = (data) => {

    }

    // ================================== 执行注册ws事件处理函数 ================================
    const executeRegisterMethod = () => {
        websocketClient.registerEventCallback(newMessageReceive, 'SEND_NEW_MSG')

        websocketClient.registerForwardEventCallback(queryHistoryMsgListForward, 'QUERY_HISTORY_MSG')
        websocketClient.registerForwardEventCallback(executeCommonToolCallForward, 'COMMON_TOOL_CALL')
    }
})()
