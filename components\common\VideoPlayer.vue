<script setup lang="ts">
import {Icon} from '#components'
import {cn} from '@/lib/utils'

// 定义组件的 props
const props = withDefaults(defineProps<{
  thumbnailSrc: string
  thumbnailAlt?: string
  videoSrc: string
  playbackMode?: 'dialog' | 'new-tab'
}>(), {
    thumbnailAlt: 'Video',
    playbackMode: 'dialog'
})

// 控制对话框的打开/关闭状态 (仅用于 dialog 模式)
const isDialogActive = ref(false)
// iframe 的 src，用于控制视频加载和停止
const iframeSrc = ref('')

// 基础视频URL处理 (确保是embed链接, 移除autoplay)
const baseVideoSrc = computed(() => {
    if (!props.videoSrc) { return '' }
    let url = props.videoSrc
    if (url.includes('youtube.com/watch?v=')) {
        url = url.replace('watch?v=', 'embed/')
    } else if (url.includes('youtu.be/')) {
        // 处理短链接, e.g. https://youtu.be/VIDEO_ID
        const videoId = url.substring(url.lastIndexOf('/') + 1)
        url = `https://www.youtube.com/embed/${videoId}`
    }

    const urlParts = new URL(url)
    urlParts.searchParams.delete('autoplay')
    // 移除其他可能影响纯净链接的参数，或者只保留必要的参数
    urlParts.searchParams.set('rel', '0')
    urlParts.searchParams.set('modestbranding', '1')
    return urlParts.toString()
})

// 用于 iframe 播放器的视频URL (添加autoplay等参数)
const videoSrcForPlayer = computed(() => {
    if (!baseVideoSrc.value) { return '' }
    const url = new URL(baseVideoSrc.value)
    url.searchParams.set('autoplay', '1')
    url.searchParams.set('controls', '1')
    // modestbranding 和 rel 应该已在 baseVideoSrc 中设置
    return url.toString()
})

// 监听 isDialogActive 状态的变化 (仅用于 dialog 模式)
watch(isDialogActive, (newIsActive) => {
    if (props.playbackMode === 'dialog') {
        if (newIsActive) {
            iframeSrc.value = videoSrcForPlayer.value
        } else {
            iframeSrc.value = ''
        }
    }
})

// 处理缩略图点击事件
function handleThumbnailClick() {
    if (props.playbackMode === 'new-tab') {
        if (baseVideoSrc.value) {
            window.open(baseVideoSrc.value, '_blank')
        }
    } else if (props.playbackMode === 'dialog') {
        isDialogActive.value = true
    }
}

function closeDialogAndReset() {
    isDialogActive.value = false
}
</script>

<template>
  <div :class="cn('relative', $attrs.class as string)">
    <!-- 缩略图和播放按钮区域 -->
    <div class="group relative cursor-pointer"
         role="button"
         tabindex="0"
         @click="handleThumbnailClick"
         @keydown.enter="handleThumbnailClick"
         @keydown.space="handleThumbnailClick">
      <img :src="props.thumbnailSrc"
           :alt="props.thumbnailAlt"
           class="w-full h-auto object-cover rounded-md border shadow-lg transition-all duration-200 ease-out group-hover:brightness-[0.8]">
      <div
        class="absolute inset-0 flex scale-[0.9] items-center justify-center rounded-lg transition-all duration-200 ease-out group-hover:scale-100">
        <div
          class="flex size-20 md:size-28 items-center justify-center rounded-full bg-primary/10 backdrop-blur-sm md:backdrop-blur-md">
          <div
            class="relative flex size-16 md:size-20 scale-100 items-center justify-center rounded-full bg-gradient-to-b from-primary/30 to-primary shadow-md transition-all duration-200 ease-out group-hover:scale-[1.1]">
            <Icon name="solar:play-bold"
                  class="w-8 h-8 md:w-10 md:h-10 scale-100 fill-white text-white transition-transform duration-200 ease-out group-hover:scale-105"
                  style="filter: drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));" />
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框 (仅在 'dialog' 模式下使用) -->
    <Dialog v-if="props.playbackMode === 'dialog'" v-model:open="isDialogActive">
      <!-- DialogTrigger 不再需要，因为点击事件由上面的 div 处理 -->
      <DialogContent class="p-0 border-0 sm:max-w-2xl md:max-w-3xl lg:max-w-5xl xl:max-w-6xl bg-black overflow-hidden"
                     @escape-key-down="closeDialogAndReset"
                     @pointer-down-outside="closeDialogAndReset">
        <div class="aspect-video relative">
          <iframe v-if="iframeSrc"
                  :src="iframeSrc"
                  title="YouTube video player"
                  frameborder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowfullscreen
                  class="w-full h-full rounded-md" />
          <DialogClose as-child class="absolute top-2 right-2 z-10" @click="closeDialogAndReset">
            <Button variant="outline"
                    size="icon"
                    class="rounded-full bg-black/50 hover:bg-black/75 text-white hover:text-white border-white/30 hover:border-white/70">
              <Icon name="lucide:x" class="w-5 h-5" />
            </Button>
          </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<style scoped>

.aspect-video {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%;
  height: 0;
}

.aspect-video iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.p-0 iframe {
  display: block;
}
</style>