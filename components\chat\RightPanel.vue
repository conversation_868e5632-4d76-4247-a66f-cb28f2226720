<script setup lang="ts">

import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'
import {<PERSON>bs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs'
import {Icon} from '#components'
import FootprintCard from './FootprintCard.vue'
import OrderItem from './OrderItem.vue'
import {Label} from '@/components/ui/label'
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select'
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover'
import {RangeCalendar} from '@/components/ui/range-calendar'
import type {DateRange} from 'radix-vue'
import logger from '@/utils/logger'

// 定义右侧面板顶部可用的操作按钮
const actions = ref([
    {label: '修改备注'},
    {label: '邀请入会'},
    {label: '发优惠券'},
    {label: '邀请入群'},
    {label: '发送合同'}
])

// --- 生成模拟数据 ---

/**
 * 创建足迹商品或推荐商品的模拟数据
 * @param count - 生成数量
 * @param tagPrefix - 标签前缀
 * @param tagClass - 标签背景色样式
 */
const createFootprints = (count: number, tagPrefix: string, tagClass: string) => {
    return Array.from({length: count}, (_, i) => ({
        id: `${tagPrefix}-${i}`,
        imageUrl: `https://placehold.co/100x100/${Math.floor(Math.random() * 16777215).toString(16)}/FFFFFF/png?text=${encodeURIComponent(tagPrefix)}${i + 1}`,
        tag: `${tagPrefix} #${i + 1}`,
        tagClass,
        daysAgo: `${i * 3 + 1}天前`,
        price: (Math.random() * 50).toFixed(2)
    }))
}

// 足迹商品列表
const footprints = ref(createFootprints(12, '足迹商品', 'bg-orange-400'))
// 咨询过的商品列表 (初始为空)
const consultedItems = ref([])
// 上次购买的商品列表
const lastPurchaseItems = ref(createFootprints(3, '上次购买', 'bg-blue-400'))
// 推荐商品列表
const recommendations = ref(createFootprints(8, '推荐', 'bg-purple-500'))

/**
 * 处理商品点击事件 (目前仅打印日志)
 * @param productId - 被点击的商品ID
 */
function handleProductClick(productId: string) {
    logger.info(`Navigating to product details for ID: ${productId}`)
}

/**
 * 创建订单的模拟数据
 * @param count - 生成数量
 * @param statusPool - 订单状态池，用于循环分配
 */
const createOrders = (count: number, statusPool: string[]) => {
    return Array.from({length: count}, (_, i) => {
        const status = statusPool[i % statusPool.length]
        let statusClass = ''
        switch (status) {
            case '已评价':
                statusClass = 'bg-purple-100 text-purple-600'
                break
            case '待发货':
                statusClass = 'bg-orange-100 text-orange-600'
                break
            case '已关闭':
                statusClass = 'bg-gray-200 text-gray-600'
                break
            default:
                statusClass = 'bg-blue-100 text-blue-600'
                break
        }
        return {
            id: `26135487159534${(60000 + i)}`,
            status,
            statusClass,
            orderDate: `2025-07-${15 - i > 9 ? 15 - i : `0${15 - i}`}`,
            orderTime: '20:57:42',
            paymentTime: status !== '已关闭' ? '20:58:00' : null,
            totalPrice: (Math.random() * 200).toFixed(2),
            itemCount: Math.ceil(Math.random() * 3),
            postage: '0.00',
            discount: (Math.random() * 10).toFixed(2),
            shippingInfo: 'x**, ***********, 上海市松江区***********',
            items: [
                {
                    imageUrl: `https://placehold.co/100x100/F4A261/FFFFFF/png?text=${status}`,
                    title: `超级会员激活码 #${i + 1}`,
                    price: (Math.random() * 50).toFixed(2),
                    originalPrice: (Math.random() * 50 + 10).toFixed(2),
                    sku: `【自动发货】SKU-${i}`,
                    id: `SKU-ID-${i}`,
                    deliveryTime: '24小时内'
                }
            ]
        }
    })
}

// 近三个月订单列表
const orders = ref(createOrders(19, ['已评价', '待发货', '已关闭', '待发货', '已评价']))
// 历史订单列表
const historyOrders = ref(createOrders(25, ['已评价', '已关闭', '已评价', '已关闭']))

// 足迹部分的子标签页配置
const footprintSubTabs = computed(() => [
    {label: '咨询宝贝', value: 'consulted', count: consultedItems.value.length},
    {label: '上次购买', value: 'last_purchase', count: lastPurchaseItems.value.length},
    {label: '足迹', value: 'footprints', count: footprints.value.length}
])

// 订单状态标签页的基础配置
const orderStatusTabs = ref([
    {label: '全部', value: 'all'},
    {label: '待发货', value: '待发货'},
    {label: '未完成', value: '未完成'},
    {label: '已完成', value: '已评价'},
    {label: '已关闭', value: '已关闭'}
])

// 近三个月订单当前激活的订单状态过滤器
const activeRecentOrderStatus = ref('all')
// 历史订单当前激活的订单状态过滤器
const activeHistoryOrderStatus = ref('all')

// 控制近三个月订单搜索区域的显示/隐藏
const showRecentSearch = ref(false)
// 控制历史订单搜索区域的显示/隐藏
const showHistorySearch = ref(false)

/**
 * 根据状态筛选订单列表
 * @param orderList - 原始订单列表
 * @param status - 目标状态
 */
const getFilteredOrders = (orderList: typeof orders.value, status: string) => {
    if (status === 'all') {
        return orderList
    }
    return orderList.filter(order => order.status === status)
}

// 过滤后的近三个月订单列表
const filteredRecentOrders = computed(() => getFilteredOrders(orders.value, activeRecentOrderStatus.value))
// 过滤后的历史订单列表
const filteredHistoryOrders = computed(() => getFilteredOrders(historyOrders.value, activeHistoryOrderStatus.value))

/**
 * 获取指定状态的订单数量
 * @param orderList - 原始订单列表
 * @param status - 目标状态
 */
const getStatusCount = (orderList: typeof orders.value, status: string) => {
    if (status === 'all') {
        return orderList.length
    }
    return orderList.filter(order => order.status === status).length
}

// 带有数量统计的近三个月订单状态标签页
const recentOrderStatusTabs = computed(() => orderStatusTabs.value.map(tab => ({
    ...tab,
    count: getStatusCount(orders.value, tab.value)
})))

// 带有数量统计的历史订单状态标签页
const historyOrderStatusTabs = computed(() => orderStatusTabs.value.map(tab => ({
    ...tab,
    count: getStatusCount(historyOrders.value, tab.value)
})))

// 近三个月订单搜索表单
const recentSearchForm = ref({
    keyword: '',
    status: '',
    paymentDate: undefined as DateRange | undefined,
    completionDate: undefined as DateRange | undefined,
    creationDate: undefined as DateRange | undefined
})

// 历史订单搜索表单
const historySearchForm = ref({
    keyword: '',
    status: '',
    paymentDate: undefined as DateRange | undefined,
    completionDate: undefined as DateRange | undefined,
    creationDate: undefined as DateRange | undefined
})

/**
 * 处理搜索事件
 * @param type - 'recent' 或 'history'
 */
const handleSearch = (type: 'recent' | 'history') => {
    const form = type === 'recent' ? recentSearchForm.value : historySearchForm.value
    logger.info(`Performing ${type} search with:`, form)
    // 此处应添加实际的搜索逻辑
}

/**
 * 清空搜索表单
 * @param type - 'recent' 或 'history'
 */
const handleClear = (type: 'recent' | 'history') => {
    const form = type === 'recent' ? recentSearchForm.value : historySearchForm.value
    form.keyword = ''
    form.status = ''
    form.paymentDate = undefined
    form.completionDate = undefined
    form.creationDate = undefined
}

/**
 * 格式化日期对象为 YYYY-MM-DD 字符串
 * @param date - Radix Vue 的 DateRange 对象中的 start 或 end
 */
const formatDate = (date: DateRange['start'] | undefined) => {
    if (!date) {
        return null
    }
    const pad = (n: number) => n.toString().padStart(2, '0')
    return `${date.year}-${pad(date.month)}-${pad(date.day)}`
}

</script>

<template>
  <div class="h-full flex flex-col bg-slate-50 dark:bg-slate-900/50 border-l">
    <!-- Top Actions -->
    <div class="p-3 border-b">
      <div class="flex flex-wrap gap-2">
        <Button v-for="action in actions"
                :key="action.label"
                variant="outline"
                size="sm">
          {{ action.label }}
        </Button>
      </div>
    </div>

    <!-- Footprints & Recommendations -->
    <div class="p-3 border-b">
      <Tabs default-value="footprints" class="w-full">
        <TabsList class="grid w-full grid-cols-2">
          <TabsTrigger value="footprints">
            足迹
          </TabsTrigger>
          <TabsTrigger value="recommendations">
            推荐
          </TabsTrigger>
        </TabsList>
        <TabsContent value="footprints" class="mt-4">
          <Tabs default-value="footprints" class="w-full">
            <div class="flex items-center justify-between mb-2">
              <TabsList class="bg-transparent p-0">
                <TabsTrigger v-for="tab in footprintSubTabs"
                             :key="tab.value"
                             :value="tab.value"
                             class="text-sm p-1 h-auto mr-4 whitespace-nowrap data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:font-semibold data-[state=active]:shadow-none rounded-none">
                  {{ tab.label }}{{ tab.count !== undefined ? `(${tab.count})` : '' }}
                </TabsTrigger>
              </TabsList>
            </div>
            <TabsContent value="footprints">
              <div v-if="footprints.length > 0" class="flex gap-2 overflow-x-auto pb-2">
                <FootprintCard v-for="item in footprints" :key="item.id" :item="item"/>
              </div>
              <p v-else class="text-center text-muted-foreground p-8">暂无足迹</p>
            </TabsContent>
            <TabsContent value="consulted">
              <p class="text-center text-muted-foreground p-8">暂无咨询宝贝</p>
            </TabsContent>
            <TabsContent value="last_purchase">
              <div v-if="lastPurchaseItems.length > 0" class="flex gap-2 overflow-x-auto pb-2">
                <FootprintCard v-for="item in lastPurchaseItems" :key="item.id" :item="item"/>
              </div>
              <p v-else class="text-center text-muted-foreground p-8">暂无上次购买记录</p>
            </TabsContent>
          </Tabs>
        </TabsContent>
        <TabsContent value="recommendations">
          <div v-if="recommendations.length > 0" class="flex flex-wrap gap-3 mt-4">
            <button v-for="item in recommendations"
                    :key="item.id"
                    class="flex flex-col items-center gap-1 text-center group"
                    @click="handleProductClick(item.id)">
              <img :src="item.imageUrl"
                   :alt="item.tag"
                   class="w-24 h-24 rounded-md object-cover group-hover:opacity-80 transition-opacity">
              <span class="text-sm font-semibold text-red-500">¥{{ item.price }}</span>
            </button>
          </div>
          <p v-else class="text-center text-muted-foreground p-8">暂无推荐</p>
        </TabsContent>
      </Tabs>
    </div>

    <!-- Orders -->
    <div class="flex-1 p-3 overflow-y-auto">
      <Tabs default-value="recent" class="w-full">
        <TabsList class="grid w-full grid-cols-2">
          <TabsTrigger value="recent">
            近3个月订单
          </TabsTrigger>
          <TabsTrigger value="history">
            历史订单
          </TabsTrigger>
        </TabsList>
        <TabsContent value="recent" class="mt-4">
          <Tabs v-model="activeRecentOrderStatus" default-value="all" class="w-full">
            <div class="flex justify-between items-center text-sm text-muted-foreground mb-2">
              <TabsList class="bg-transparent p-0 flex-wrap justify-start">
                <TabsTrigger v-for="tab in recentOrderStatusTabs"
                             :key="tab.value"
                             :value="tab.value"
                             class="text-sm p-1 h-auto mr-4 whitespace-nowrap data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:font-semibold data-[state=active]:shadow-none rounded-none">
                  {{ tab.label }}({{ tab.count }})
                </TabsTrigger>
              </TabsList>
              <div class="flex items-center gap-3 shrink-0 ml-4">
                <Icon name="lucide:refresh-cw" class="w-4 h-4 cursor-pointer"/>
                <Icon name="lucide:search"
                      class="w-4 h-4 cursor-pointer"
                      @click="showRecentSearch = !showRecentSearch"/>
              </div>
            </div>

            <div v-if="showRecentSearch" class="bg-slate-100 dark:bg-slate-800/50 p-4 rounded-lg border my-4 space-y-4">
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">订单搜索</Label>
                <div class="flex-1 min-w-0">
                  <Input v-model="recentSearchForm.keyword" placeholder="订单编号/运单号/退款编号/商品ID"/>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">订单状态</Label>
                <div class="flex-1 min-w-0">
                  <Select v-model="recentSearchForm.status">
                    <SelectTrigger>
                      <SelectValue placeholder="请选择"/>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="tab in orderStatusTabs" :key="tab.value" :value="tab.value">
                        {{ tab.label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">付款时间</Label>
                <div class="flex-1 min-w-0">
                  <Popover>
                    <PopoverTrigger as-child>
                      <Button variant="outline" class="w-full justify-start text-left font-normal h-9">
                        <Icon name="lucide:calendar" class="mr-2 h-4 w-4"/>
                        <template v-if="recentSearchForm.paymentDate?.start">
                          {{ formatDate(recentSearchForm.paymentDate.start) }} - {{
                            formatDate(recentSearchForm.paymentDate.end)
                          }}
                        </template>
                        <template v-else>
                          <span class="text-muted-foreground">起始日期 — 结束日期</span>
                        </template>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-auto p-0" align="start">
                      <RangeCalendar v-model="recentSearchForm.paymentDate"/>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">完成时间</Label>
                <div class="flex-1 min-w-0">
                  <Popover>
                    <PopoverTrigger as-child>
                      <Button variant="outline" class="w-full justify-start text-left font-normal h-9">
                        <Icon name="lucide:calendar" class="mr-2 h-4 w-4"/>
                        <template v-if="recentSearchForm.completionDate?.start">
                          {{ formatDate(recentSearchForm.completionDate.start) }} - {{
                            formatDate(recentSearchForm.completionDate.end)
                          }}
                        </template>
                        <template v-else>
                          <span class="text-muted-foreground">起始日期 — 结束日期</span>
                        </template>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-auto p-0" align="start">
                      <RangeCalendar v-model="recentSearchForm.completionDate"/>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">创建时间</Label>
                <div class="flex-1 min-w-0">
                  <Popover>
                    <PopoverTrigger as-child>
                      <Button variant="outline" class="w-full justify-start text-left font-normal h-9">
                        <Icon name="lucide:calendar" class="mr-2 h-4 w-4"/>
                        <template v-if="recentSearchForm.creationDate?.start">
                          {{ formatDate(recentSearchForm.creationDate.start) }} - {{
                            formatDate(recentSearchForm.creationDate.end)
                          }}
                        </template>
                        <template v-else>
                          <span class="text-muted-foreground">起始日期 — 结束日期</span>
                        </template>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-auto p-0" align="start">
                      <RangeCalendar v-model="recentSearchForm.creationDate"/>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div class="flex justify-end gap-2">
                <Button size="sm" @click="handleSearch('recent')">搜索</Button>
                <Button size="sm" variant="outline" @click="handleClear('recent')">清空</Button>
                <Button size="sm" variant="ghost" @click="showRecentSearch = false">取消</Button>
              </div>
            </div>

            <div v-if="filteredRecentOrders.length > 0" class="space-y-3 mt-2">
              <OrderItem v-for="order in filteredRecentOrders" :key="order.id" :order="order"/>
            </div>
            <div v-else>
              <p class="text-center text-muted-foreground p-8">
                暂无相关订单
              </p>
            </div>
          </Tabs>
        </TabsContent>
        <TabsContent value="history" class="mt-4">
          <Tabs v-model="activeHistoryOrderStatus" default-value="all" class="w-full">
            <div class="flex justify-between items-center text-sm text-muted-foreground mb-2">
              <TabsList class="bg-transparent p-0 flex-wrap justify-start">
                <TabsTrigger v-for="tab in historyOrderStatusTabs"
                             :key="tab.value"
                             :value="tab.value"
                             class="text-sm p-1 h-auto mr-4 whitespace-nowrap data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:font-semibold data-[state=active]:shadow-none rounded-none">
                  {{ tab.label }}({{ tab.count }})
                </TabsTrigger>
              </TabsList>
              <div class="flex items-center gap-3 shrink-0 ml-4">
                <Icon name="lucide:refresh-cw" class="w-4 h-4 cursor-pointer"/>
                <Icon name="lucide:search"
                      class="w-4 h-4 cursor-pointer"
                      @click="showHistorySearch = !showHistorySearch"/>
                <Icon name="lucide:menu" class="w-4 h-4 cursor-pointer"/>
              </div>
            </div>

            <div v-if="showHistorySearch"
                 class="bg-slate-100 dark:bg-slate-800/50 p-4 rounded-lg border my-4 space-y-4">
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">订单搜索</Label>
                <div class="flex-1 min-w-0">
                  <Input v-model="historySearchForm.keyword" placeholder="订单编号/运单号/退款编号/商品ID"/>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">订单状态</Label>
                <div class="flex-1 min-w-0">
                  <Select v-model="historySearchForm.status">
                    <SelectTrigger>
                      <SelectValue placeholder="请选择"/>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="tab in orderStatusTabs" :key="tab.value" :value="tab.value">
                        {{ tab.label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">付款时间</Label>
                <div class="flex-1 min-w-0">
                  <Popover>
                    <PopoverTrigger as-child>
                      <Button variant="outline" class="w-full justify-start text-left font-normal h-9">
                        <Icon name="lucide:calendar" class="mr-2 h-4 w-4"/>
                        <template v-if="historySearchForm.paymentDate?.start">
                          {{ formatDate(historySearchForm.paymentDate.start) }} - {{
                            formatDate(historySearchForm.paymentDate.end)
                          }}
                        </template>
                        <template v-else>
                          <span class="text-muted-foreground">起始日期 — 结束日期</span>
                        </template>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-auto p-0" align="start">
                      <RangeCalendar v-model="historySearchForm.paymentDate"/>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">完成时间</Label>
                <div class="flex-1 min-w-0">
                  <Popover>
                    <PopoverTrigger as-child>
                      <Button variant="outline" class="w-full justify-start text-left font-normal h-9">
                        <Icon name="lucide:calendar" class="mr-2 h-4 w-4"/>
                        <template v-if="historySearchForm.completionDate?.start">
                          {{ formatDate(historySearchForm.completionDate.start) }} - {{
                            formatDate(historySearchForm.completionDate.end)
                          }}
                        </template>
                        <template v-else>
                          <span class="text-muted-foreground">起始日期 — 结束日期</span>
                        </template>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-auto p-0" align="start">
                      <RangeCalendar v-model="historySearchForm.completionDate"/>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Label class="w-20 text-right shrink-0">创建时间</Label>
                <div class="flex-1 min-w-0">
                  <Popover>
                    <PopoverTrigger as-child>
                      <Button variant="outline" class="w-full justify-start text-left font-normal h-9">
                        <Icon name="lucide:calendar" class="mr-2 h-4 w-4"/>
                        <template v-if="historySearchForm.creationDate?.start">
                          {{ formatDate(historySearchForm.creationDate.start) }} - {{
                            formatDate(historySearchForm.creationDate.end)
                          }}
                        </template>
                        <template v-else>
                          <span class="text-muted-foreground">起始日期 — 结束日期</span>
                        </template>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-auto p-0" align="start">
                      <RangeCalendar v-model="historySearchForm.creationDate"/>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div class="flex justify-end gap-2">
                <Button size="sm" @click="handleSearch('history')">搜索</Button>
                <Button size="sm" variant="outline" @click="handleClear('history')">清空</Button>
                <Button size="sm" variant="ghost" @click="showHistorySearch = false">取消</Button>
              </div>
            </div>

            <div v-if="filteredHistoryOrders.length > 0" class="space-y-3 mt-2">
              <OrderItem v-for="order in filteredHistoryOrders" :key="order.id" :order="order"/>
            </div>
            <div v-else>
              <p class="text-center text-muted-foreground p-8">
                暂无相关订单
              </p>
            </div>
          </Tabs>
        </TabsContent>
      </Tabs>
    </div>
  </div>
</template>
