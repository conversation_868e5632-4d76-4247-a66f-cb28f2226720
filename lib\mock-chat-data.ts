import type {BackendMessage, PageData, PlatformUserinfo, ProductData} from '@/types/chat'
import {MessageCategory} from '@/types/chat'

const mockUsers: Record<string, PlatformUserinfo> = {
    'user-me': {userId: 'user-me', displayName: 'AI客服', avatar: 'https://github.com/shadcn.png'},
    'contact-ongoing-1': {
        userId: 'contact-ongoing-1',
        displayName: '正在接待-小明',
        avatar: 'https://avatars.githubusercontent.com/u/22224712?v=4'
    },
    'contact-ongoing-2': {
        userId: 'contact-ongoing-2',
        displayName: '正在接待-小红',
        avatar: 'https://avatars.githubusercontent.com/u/20119879?v=4'
    },
    'contact-1': {userId: 'contact-1', displayName: 'xcyea', avatar: 'https://github.com/radix-vue.png'},
    'contact-2': {
        userId: 'contact-2',
        displayName: 'angiegogo2',
        avatar: 'https://avatars.githubusercontent.com/u/10189633?v=4'
    },
    'contact-no-order-1': {
        userId: 'contact-no-order-1',
        displayName: '咨询未下单-1',
        avatar: 'https://avatars.githubusercontent.com/u/75498391?v=4'
    },
    'contact-unpaid-1': {
        userId: 'contact-unpaid-1',
        displayName: '咨询未付款-1',
        avatar: 'https://avatars.githubusercontent.com/u/1024025?v=4'
    },
    'contact-3': {
        userId: 'contact-3',
        displayName: 'tb992120244',
        avatar: 'https://avatars.githubusercontent.com/u/20261975?v=4'
    },
    'contact-4': {
        userId: 'contact-4',
        displayName: 'asde',
        avatar: 'https://avatars.githubusercontent.com/u/10189633?v=4'
    },
    'contact-5': {
        userId: 'contact-5',
        displayName: 'qinginginging...',
        avatar: 'https://avatars.githubusercontent.com/u/20261975?v=4'
    }
}

const generateMockProduct = (id: number): ProductData => ({
    id,
    title: `这是商品标题${id}，这是一个非常好的商品，强烈推荐购买！`,
    price: (Math.random() * 100).toFixed(2),
    sales: Math.floor(Math.random() * 1000),
    image: `https://picsum.photos/seed/${id}/200/200`,
    link: `https://item.taobao.com/item.htm?id=${id}`
})

function generateConversation(contactId: string, messageCount: number): BackendMessage[] {
    const messages: BackendMessage[] = []
    const contactUser = mockUsers[contactId]
    const currentUser = mockUsers['user-me']

    if (!contactUser)
    {return []}

    for (let i = 0; i < messageCount; i++) {
        const fromUser = i % 3 === 0 ? currentUser : contactUser
        const toUser = i % 3 === 0 ? contactUser : currentUser
        const msgId = `msg-${contactId}-${i}`
        const sendTime = Date.now() - (messageCount - i) * 1000 * 60 * (Math.random() * 5 + 1)

        if (i > 5 && i % 7 === 0) {
            messages.push({
                id: msgId,
                conversationId: contactId,
                sendTime,
                sender: fromUser,
                receiver: toUser,
                category: MessageCategory.PRODUCT_CONSULT,
                list: [{type: 'PRODUCT_CONSULT', data: generateMockProduct(i)}]
            })
        } else {
            messages.push({
                id: msgId,
                conversationId: contactId,
                sendTime,
                sender: fromUser,
                receiver: toUser,
                category: MessageCategory.TEXT,
                list: [
                    {type: 'TEXT', data: `这是关于 ${contactUser.displayName} 的第 ${i + 1} 条历史消息。`},
                    {
                        type: 'EMOJI',
                        data: {
                            meaning: '[微笑]',
                            url: 'https://cdn.jsdelivr.net/gh/twitter/twemoji@v14.0.2/assets/72x72/1f600.png'
                        }
                    }
                ]
            })
        }
    }
    return messages
}

const allMockMessages: Record<string, BackendMessage[]> = Object.keys(mockUsers)
    .filter(id => id !== 'user-me')
    .reduce((acc, contactId) => {
        const messageCount = Math.floor(Math.random() * 31) + 20 // 20 to 50 messages
        acc[contactId] = generateConversation(contactId, messageCount)
        return acc
    }, {} as Record<string, BackendMessage[]>)

/**
 * Simulates fetching a page of historical messages from the backend.
 * @param contactId - The ID of the contact.
 * @param pageNum - The page number to fetch (1-indexed).
 * @param pageSize - The number of messages per page.
 * @returns A promise that resolves to a PageData object.
 */
export function fetchMockHistoryMessages(
    contactId: string,
    pageNum: number = 1,
    pageSize: number = 20
): Promise<PageData<BackendMessage>> {
    return new Promise((resolve) => {
        setTimeout(() => {
            const messagesForContact = allMockMessages[contactId] || []
            const totalMessages = messagesForContact.length
            const startIndex = totalMessages - (pageNum * pageSize)
            const endIndex = startIndex + pageSize

            const safeStartIndex = Math.max(0, startIndex)
            const safeEndIndex = Math.min(totalMessages, endIndex)

            const paginatedMessages = messagesForContact.slice(safeStartIndex, safeEndIndex)

            const response: PageData<BackendMessage> = {
                result: paginatedMessages,
                additionalData: {
                    hasMore: safeStartIndex > 0
                }
            }
            resolve(response)
        }, 500) // Simulate network delay
    })
}
