<script setup lang="ts">
import type {ConfigPricingPeriod, ConfigPricingPlan, PricingPageConfig} from '@/types/site/pricingConfig'
import pricingPageData from '@/config/pricingConfig'
import {getLocalizedConfigText} from '@/utils/i18n'
import {payApi} from '~/utils/api/payApi'
import type {ProductShowResponse} from '~/types/api/response/ProductShowResponse'
import type {CheckoutParams} from '~/types/api/params/CheckoutParams'
import {useMessage} from '~/composables/useMessage'

// 将导入的配置断言为正确的类型
const typedPricingConfig = computed(() => pricingPageData as PricingPageConfig)
const periodPlansMap = ref<Map<string, PricePlan[]>>(new Map)
const {message} = useMessage()

const getCurrencySymbol = (currency: string): { symbol: string, ratio: number } => {
    if (currency.toLowerCase() === 'usd') {
        return {symbol: '$', ratio: 100}
    }
    return {symbol: '$', ratio: 100}
}

interface PricePlan extends ConfigPricingPlan {
    product: ProductShowResponse
}

const fetchProductList = async () => {
    const {productIdList} = typedPricingConfig.value
    if (!productIdList || productIdList.length === 0) {
        logger.error('当前尚未配置商品列表')
        return
    }
    const {code, data} = await payApi.queryProductList({
        productIdList,
        showProviderType: typedPricingConfig.value.queryPayProvider
    })
    if (code !== 200 || !data) {
        logger.error(`未能获取${productIdList}对应的商品信息`)
        return
    }

    Object.keys(data).forEach(key => {
        const plans = data[key].map(product => {
            let {params} = product
            if (!params) {
                params = '{}'
            }
            let pricePlan: ConfigPricingPlan
            try {
                pricePlan = JSON.parse(params) as ConfigPricingPlan
            } catch (e) {
                logger.error('解析json失败')
                return undefined
            }
            const currencySymbol = getCurrencySymbol(product.currency)
            // 美分
            if (product.allowDiscount && product.discountRate) {
                pricePlan.price = `${currencySymbol.symbol} ${product.price * (1 - product.discountRate) / currencySymbol.ratio}`
                pricePlan.originalPrice = `${currencySymbol.symbol} ${product.price / currencySymbol.ratio}`
            } else {
                pricePlan.price = `${currencySymbol.symbol} ${product.price / currencySymbol.ratio}`
                pricePlan.originalPrice = undefined
            }
            pricePlan.isPopular = product.popularStatus

            return {...pricePlan, product}
        }).filter(t => t !== undefined)
        periodPlansMap.value.set(key, plans)
    })
}

fetchProductList()

// 本地化后的付费周期和计划
const localizedPeriods = computed(() => {
    return typedPricingConfig.value.periods.map((p: ConfigPricingPeriod) => {
        let plans = periodPlansMap.value.get(p.key)
        if (plans === undefined) {
            plans = []
        }

        return {
            key: p.key,
            showText: getLocalizedConfigText(p.showText),
            periodSuffixText: getLocalizedConfigText(p.periodSuffixText),
            plans: plans.map((plan: PricePlan) => {
                if (!plan.features) {
                    plan.features = []
                }
                return {
                    title: getLocalizedConfigText(plan.title),
                    description: getLocalizedConfigText(plan.description),
                    price: plan.price,
                    originalPrice: plan.originalPrice,
                    isPopular: plan.isPopular,
                    popularText: plan.popularText ? getLocalizedConfigText(plan.popularText) : undefined,
                    features: plan.features.map(feature => getLocalizedConfigText(feature)),
                    buttonText: getLocalizedConfigText(plan.buttonText),
                    buttonIcon: plan.buttonIcon,
                    product: plan.product
                }
            })
        }
    })
})

const period = ref(localizedPeriods.value.length > 0 ? localizedPeriods.value[0].key : 'annually')

function updatePeriod(newPeriodKey: string) {
    period.value = newPeriodKey
}

const currentPlans = computed(() => {
    const found = localizedPeriods.value.find(p => p.key === period.value)
    return found ? found.plans : []
})

const currentPeriodSuffix = computed(() => {
    const foundPeriod = localizedPeriods.value.find(p => p.key === period.value)
    if (foundPeriod && foundPeriod.periodSuffixText && foundPeriod.periodSuffixText.length > 0) {
        return `/${foundPeriod.periodSuffixText}`
    }
    return ''
})

// 获取本地化的静态文本
const sectionHeader = computed(() => getLocalizedConfigText(typedPricingConfig.value.sectionHeader))
const mainTitle = computed(() => getLocalizedConfigText(typedPricingConfig.value.mainTitle))
const descriptionText = computed(() => getLocalizedConfigText(typedPricingConfig.value.descriptionText))

const handleCheckout = async (product: ProductShowResponse) => {
    const params: CheckoutParams = {
        productId: product.id,
        buyCount: 1,
        paymentProvider: typedPricingConfig.value.queryPayProvider,
        subscriptionType: product.subscriptionType
    }

    const {code, data, message: responseMessage, requestId} = await payApi.checkout(params)
    if (code !== 200) {
        message.error(responseMessage, {showSupport: true, requestId})
        return
    }
    if (!data) {
        message.error('未能获取到结账数据', {showSupport: true})
        return
    }
    if (data.redirectUrl) {
        await navigateTo(data.redirectUrl, {
            external: true,
            open: {target: '_blank'}
        })
    }
}

</script>

<template>
  <div class="py-16">
    <slot name="before-main-header" :pricing-config="typedPricingConfig" :localized-periods-config="localizedPeriods"/>
    <div class="text-center mb-6 md:mb-12">
      <div class="text-primary mb-6 text-sm font-medium" v-html="sectionHeader"/>
      <h2 class="text-3xl md:text-4xl font-bold mb-6" v-html="mainTitle"/>
      <slot name="main-description"
            :description="descriptionText"
            :main-title="mainTitle"
            :section-header="sectionHeader">
        <p class="text-sm mb-6 md:text-base text-muted-foreground max-w-2xl mx-auto" v-html="descriptionText"/>
      </slot>

      <slot name="period-toggle-container"
            :localized-periods="localizedPeriods"
            :current-period-key="period"
            :update-period="updatePeriod">
        <div v-if="localizedPeriods.length >= 2" class="mb-6 inline-flex items-center rounded-full p-1 bg-muted">
          <template v-for="item in localizedPeriods" :key="item.key">
            <slot name="period-toggle-button"
                  :period-item="item"
                  :is-active="period === item.key"
                  :select-period="() => updatePeriod(item.key)">
              <button class="px-5 py-1.5 rounded-full text-sm font-medium transition-colors"
                      :class="period === item.key ? 'bg-primary text-primary-foreground shadow-sm' : 'text-muted-foreground hover:bg-accent'"
                      @click="updatePeriod(item.key)">
                {{ item.showText }}
              </button>
            </slot>
          </template>
        </div>
      </slot>
    </div>

    <div class="grid grid-cols-1 gap-8" :class="`md:grid-cols-${currentPlans.length}`">
      <div v-for="(plan, index) in currentPlans"
           :key="index"
           :class="[
             'border rounded-lg p-6 flex flex-col',
             plan.isPopular ? 'border-2 border-primary md:-mt-8 shadow-lg bg-card' : 'border-border',
           ]">
        <slot name="plan-card-header" :plan="plan" :current-period-key="period"/>
        <div :class="plan.isPopular ? 'md:pt-8' : ''">
          <div>
            <slot name="plan-top-section" :plan="plan" :current-period-key="period">
              <div class="flex items-center justify-between mb-3">
                <h3 :class="['text-lg md:text-xl font-medium', plan.isPopular ? 'text-primary' : '']"
                    v-html="plan.title"/>
                <span v-if="plan.isPopular && plan.popularText"
                      class="bg-primary text-primary-foreground text-xs font-medium px-3 py-1 rounded-full">
                  {{ plan.popularText }}
                </span>
              </div>

              <p class="mb-4 md:mb-6 text-muted-foreground text-xs md:text-sm" v-html="plan.description"/>
              <div class="mb-4 md:mb-6 flex items-baseline">
                <span v-if="plan.originalPrice" class="text-sm text-muted-foreground line-through mr-2">{{
                  plan.originalPrice
                }}</span>
                <span class="text-3xl md:text-4xl font-bold mr-1">{{ plan.price }}</span>
                <span class="text-muted-foreground text-sm">{{ currentPeriodSuffix }}</span>
              </div>
            </slot>
            <slot name="plan-card-after-price"
                  :plan="plan"
                  :current-period-key="period"
                  :current-period-suffix="currentPeriodSuffix"/>

            <div class="hidden md:block">
              <slot name="plan-buy-button-pc" :plan="plan" :current-period-key="period">
                <button
                  class="w-full rounded-lg py-2 px-4 text-sm font-medium hidden md:block bg-primary text-primary-foreground hover:bg-primary/90"
                  @click="handleCheckout(plan.product)">
                  <Icon v-if="plan.buttonIcon" :name="plan.buttonIcon" class="mr-2 h-4 w-4 inline-block"/>
                  {{ plan.buttonText }}
                </button>
              </slot>
            </div>

            <slot name="plan-card-before-features" :plan="plan" :current-period-key="period"/>
          </div>
          <slot name="plan-features-list" :plan="plan" :current-period-key="period">
            <ul class="mt-6 md:mt-8 space-y-3 md:space-y-4 flex-grow">
              <li v-for="(feature, featureIndex) in plan.features"
                  :key="featureIndex"
                  class="flex items-start text-sm text-foreground">
                <Icon name="material-symbols:fitbit-check-small"
                      class="h-4 w-4 md:h-5 md:w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5"/>
                <span v-html="feature"/>
              </li>
            </ul>
          </slot>
          <slot name="plan-card-after-features" :plan="plan" :current-period-key="period"/>

          <div class="mt-6 md:hidden">
            <slot name="plan-buy-button-mobile"
                  class="md:hidden"
                  :plan="plan"
                  :current-period-key="period">
              <button
                class="w-full rounded-lg py-2 px-4 text-sm font-medium block md:hidden bg-primary text-primary-foreground hover:bg-primary/90">
                <Icon v-if="plan.buttonIcon" :name="plan.buttonIcon" class="mr-2 h-4 w-4 inline-block"/>
                {{ plan.buttonText }}
              </button>
            </slot>
          </div>
        </div>
      </div>
    </div>

    <slot name="after-all-cards"
          :current-period-key="period"
          :localized-periods="localizedPeriods"
          :current-plans="currentPlans"
          :typed-pricing-config="typedPricingConfig"/>
  </div>
</template>
